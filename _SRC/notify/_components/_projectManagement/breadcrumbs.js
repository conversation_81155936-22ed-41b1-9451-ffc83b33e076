Factory.register("breadcrumbs", function (sb) {
  // view registrations
  var adminURL = "../../api/";
  var cachedLayers = {};
  var cachedRightTrayPageObjects = {};
  var registerNavItems = [];
  var hqTools = [];
  var teamTools = [];
  var jobTypeTools = [];
  var objectTools = [];
  var myStuff = [];
  var navItems = [];
  var customViews = [];
  var projectTools = [];
  var objectViews = [];
  var editViews = [];
  var currentUser = {};
  var ErrorPage = {
    dom: function (ui) {
      ui.empty();
      ui.makeNode("msg", "div", {
        css: "ui icon error message",
        text:
          '<i class="exclamation circle icon"></i>' +
          '<div class="content">' +
          '<div class="header">Oops--looks like this page doesn\'t exist</div>' +
          '<p>Go back to <a class="ui link" onclick="window.history.back()">previous page<a>.</p>' +
          "</div>",
      });
      ui.patch();
    },
    header: false,
    icon: "remove",
    id: "error-page",
    title: "Error Page",
    type: "error",
  };

  // application state
  var App_State = {};
  var App_UI;
  var Layers = false;
  var mainUI = {};
  var mainView_UI = {};
  var TopNav = false;
  var LogoContainer = false;
  var Components = {
    pageTag: {},
  };
  var History = [window.location.href];
  var appSettings = {};
  var spaceNavigationColOpen = true;
  var rightTray = {};
  var rightTrayContainer = {};
  var rightTrayOpen = false;
  var autoClosed = false;
  var lastVersionCheck = 5; // seconds
  var newVersionButton = "hidden";
  var Modal = {};
  var ModalOnClose = function () {};
  var Floater = {};
  var Portals = [];
  var RootInstance = {};
  var onAddPagesArea = false;

  // instance data
  var companyLogo;
  var hq = {};

  // object data cache
  var Data = {
    objects: [],
  };

  var IsInitialPageLoad = true;
  var DrawPageMenu = function () {};
  var DrawToolBarMenu = function () {};
  var DrawPageSelection = function () {};
  var sideNavButtonUI = {};
  var systemToolbar_refresh = function () {};

  // !DELETE (lns 76 - 90) -- Candidate for deletion added on 01-06-20
  function toggleFullScreen() {
    var doc = window.document;
    var docEl = doc.documentElement;

    var requestFullScreen =
      docEl.requestFullscreen ||
      docEl.mozRequestFullScreen ||
      docEl.webkitRequestFullScreen ||
      docEl.msRequestFullscreen;
    var cancelFullScreen =
      doc.exitFullscreen ||
      doc.mozCancelFullScreen ||
      doc.webkitExitFullscreen ||
      doc.msExitFullscreen;

    if (
      !doc.fullscreenElement &&
      !doc.mozFullScreenElement &&
      !doc.webkitFullscreenElement &&
      !doc.msFullscreenElement
    ) {
      requestFullScreen.call(docEl);
    } else {
      cancelFullScreen.call(doc);
    }
  }

  // single object system functions

  function move_obj() {}

  function archive_obj() {}

  function updateNotifyList(object, users, callback) {
    var notifyList = _.clone(object.notify);
    var usersList = users || [+sb.data.cookie.userId];
    var updatedList;

    if (_.intersection(notifyList, usersList).length == usersList.length) {
      updatedList = _.difference(notifyList, usersList);
    } else {
      updatedList = _.union(notifyList, usersList);
    }

    updatedList = _.uniq(updatedList);

    sb.data.db.obj.update(
      "",
      { id: object.id, notify: updatedList },
      function (resp) {
        if (callback && typeof callback == "function") callback(resp);
      }
    );
  }

  // utility funcs

  function parse_page_data(pageData, i) {
    var data = pageData.split("-");
    var ret = {
      key: i,
      type: data[0],
      view: data[1],
      objectId: parseInt(data[2]),
      title: data[3],
    };

    return ret;
  }

  function get_company_logo(appConfig, callback) {
    sb.data.db.obj.getWhere(
      "company_logo",
      { is_primary: "yes", childObjs: 1 },
      function (logo) {
        callback(logo);
      }
    );
  }

  function get_page_state(urlData, callback, state, currentView) {
    function get_page_object(state, callback) {
      if (state.hasOwnProperty("id") && parseInt(state.id) > 0) {
        var selectionObj = 1;

        if (currentView && currentView.header && currentView.header.select) {
          selectionObj = currentView.header.select;
        } else if (currentView && currentView.select) {
          selectionObj = currentView.select;
        }

        state.pageObject = _.findWhere(Data.objects, { id: state.id });
        if (state.pageObject) {
          state.pageObjectType = state.pageObject.object_bp_type;
          return callback(state);
        }

        sb.data.db.obj.getById(
          "",
          state.id,
          function (pageObject) {
            if (pageObject) {
              Data.objects.push(pageObject);
              state.pageObject = pageObject;
              state.pageObjectType = pageObject.object_bp_type;
            } else {
              // !TODO: if we start returning the "deleted" empty objects
              // here, we can create an error page with an unarchive button
              // and route there
              state.toErrorPage = true;
            }

            callback(state);
          },
          selectionObj,
          true
        );
      } else {
        callback(state);
      }
    }

    var pages = _.map(urlData, parse_page_data);

    // get most downstream team and project groups if they exist
    var groupsToGet = {
      team: 0,
      // 			, project:0
    };
    _.each(pages, function (page) {
      if (page.type === "o" && page.view === "team") {
        groupsToGet.team = page.objectId;
      }
    });

    get_page_object(state, function (state) {
      if (groupsToGet.team) {
        App_State.team = _.findWhere(Data.objects, { id: groupsToGet.team });

        // check local cache
        if (App_State.team) {
          callback(state);
        } else {
          sb.data.db.obj.getById(
            "groups",
            [groupsToGet.team, groupsToGet.project],
            function (groups) {
              // cache team
              Data.objects.push(_.findWhere(groups, { id: groupsToGet.team }));
              App_State.team = _.findWhere(groups, { id: groupsToGet.team });

              callback(state);
            }
          );
        }
      } else {
        delete App_State.team;
        delete App_State.project;

        callback(state);
      }
    });
  }

  function get_instance_data(data) {
    // !TODO: Clear entity types

    sb.data.db.obj.getAll(
      "entity_type",
      function (entityTypes) {
        var entitySetup = {
          entityTypes: entityTypes,
        };

        if (data.hasOwnProperty("toolRegistration")) {
          entitySetup.toolRegistration = data.toolRegistration;
        }

        sb.notify({
          type: "entity-types-received",
          data: entitySetup,
        });

        sb.notify({
          type: "load-permissions",
          data: {
            onLoad: function (permissions) {
              if (
                data.hasOwnProperty("onComplete") &&
                typeof data.onComplete == "function"
              )
                data.onComplete();
            },
          },
        });
      },
      undefined,
      undefined,
      undefined,
      true
    );
  }

  function get_startup_data(urlData, callback) {
    // Set variables
    var instance = sb.data.cookie.get("instance");

    if (!instance) {
      sb.notify({
        type: "logout-user",
        data: {},
      });

      return;
    }

    if (appConfig.instance !== instance) {
      sb.notify({
        type: "get-accounts",
        data: {
          callback: function (accounts) {
            var accountInstances = _.pluck(accounts, "instance");

            if (accountInstances.includes(appConfig.instance)) {
              var fromAccount = _.findWhere(accounts, { instance: instance });
              var toAccount = _.findWhere(accounts, {
                instance: appConfig.instance,
              });

              if (fromAccount && toAccount) {
                var fromInstanceName = fromAccount.instance.toUpperCase();
                if (!_.isEmpty(fromAccount.instanceName)) {
                  fromInstanceName = fromAccount.instanceName;
                }

                var toInstanceName = toAccount.instance.toUpperCase();
                if (!_.isEmpty(toAccount.instanceName)) {
                  toInstanceName = toAccount.instanceName;
                }

                sb.notify({
                  type: "logout-user",
                  data: {
                    toAccount: toAccount,
                    message:
                      "You are about to be logged out of " +
                      fromInstanceName +
                      " and logged in to " +
                      toInstanceName +
                      ".",
                    refreshPage: true,
                  },
                });
              } else {
                window.location.href = "/app/" + instance;
              }
            } else {
              window.location.href = "/app/" + instance;
            }
          },
        },
      });
    } else {
      sb.data.db.controller("getStartupData", {}, function (data) {
        var pageData = {};

        companyLogo = data.companyLogo;
        appConfig.companyLogoObj = data.companyLogo;
        appConfig.user = data.user;
        currentUser = data.user;
        appSettings = data.appSettings;
        userSettings = data.userSettings;
        pageData.headquarters = data.headquarters;
        Portals = data.portalTokens;

        // If the HQ isn't created, then take the user back and show the welcome screen
        if (!data.headquarters.hasOwnProperty("id")) {
          callback(pageData);
          return;
        }

        // If for some reason the user isn't set here, then take them back to the login screen
        if (!data.user.hasOwnProperty("id")) {
          sb.notify({
            type: "logout-user",
            data: {
              cookieError: true,
            },
          });

          return;
        }

        _.each(registerNavItems, function (item) {
          if (appSettings && appSettings[0]) {
            if (!appSettings[0].settings) {
              appSettings[0].settings.push(item);
            } else if (!appSettings[0].settings[item.id]) {
              appSettings[0].settings[item.id] = {};
            }

            if (!userSettings[0].settings) {
              userSettings[0].settings.push(item);
            } else if (!userSettings[0].settings[item.id]) {
              userSettings[0].settings[item.id] = {};
            }
          }
        });

        sb.notify({
          type: "load-permissions",
          data: {
            onLoad: function (permissions) {
              sb.notify({
                type: "entity-types-received",
                data: {
                  entityTypes: data.entityTypes,
                },
              });

              get_page_state(urlData, callback, pageData);
            },
          },
        });
      });
    }
  }

  function get_transition_dir(newUrl) {
    var oldDepth = History[0].split("&").length;
    var newDepth = newUrl.split("&").length;

    if (oldDepth > newDepth) {
      return "LEFT";
    } else if (oldDepth < newDepth) {
      return "RIGHT";
    } else {
      return "DOWN";
    }
  }

  function versionCheck() {
    function startTimer() {
      setInterval(function () {
        if (lastVersionCheck == 0) {
          // reset the timer before the async call so setInterval doesn't start another one
          lastVersionCheck = 600;

          sb.data.db.controller(
            "getApplicationBuild",
            {},
            function (currentAppBuild) {
              if (
                currentAppBuild.build != PAGODA_APP_BUILD.toString() &&
                newVersionButton == "hidden"
              ) {
                dom.makeNode("cont", "div", {});
                dom.cont.makeNode("btn", "div", {
                  css: "ui mini floating black icon message animated slideInUp",
                  style:
                    "min-width:275px;box-shadow:0 0 0 1px rgba(34,36,38,.22) inset, 0 2px 4px 0 rgba(34,36,38,.12), 0 2px 10px 0 rgba(34,36,38,.15);-webkit-box-shadow:0 0 0 1px rgba(34,36,38,.22) inset, 0 2px 4px 0 rgba(34,36,38,.12), 0 2px 10px 0 rgba(34,36,38,.15);",
                });
                dom.cont.btn.makeNode("loading", "div", {
                  css: "exclamation icon",
                  tag: "i",
                });
                dom.cont.btn.makeNode("close", "div", {
                  css: "close icon",
                  tag: "i",
                });
                dom.cont.btn.makeNode("content", "div", { css: "content" });
                dom.cont.btn.content.makeNode("header", "div", {
                  css: "header",
                  text: "New version available",
                });
                dom.cont.btn.content
                  .makeNode("refresh", "div", {
                    text: "Click here to get it.",
                    style: "cursor:pointer; text-decoration:underline;",
                  })
                  .notify(
                    "click",
                    {
                      type: "run-method",
                      data: {
                        run: function () {
                          dom.cont.btn.loading.css(
                            "notched circle loading icon"
                          );

                          $.ajax({
                            url: window.location.href,
                            headers: {
                              Pragma: "no-cache",
                              Expires: -1,
                              "Cache-Control": "no-cache",
                            },
                          }).done(function () {
                            setTimeout(function () {
                              location.reload(true);
                            }, 1500);
                          });
                        },
                      },
                    },
                    sb.moduleId
                  );

                dom.build();

                newVersionButton = "visible";

                $(".message .close").on("click", function () {
                  $(this).closest(".message").removeClass("slideInRight");
                  $(this).closest(".message").addClass("fadeOut");
                });
              }
            }
          );
        } else {
          lastVersionCheck--;
        }
      }, 1000);
    }

    $("body").append('<div class="versionUpdateContainer"></div>');

    var dom = sb.dom.make(".versionUpdateContainer");

    startTimer();
  }

  // view funcs

  function app_ui(ui, urlData, doneCallback, refresh) {
    var isAdmin = false;
    var is_portal = false;

    // get current layer
    var rootPage = "";
    var EditModal;
    var currentView = false;
    var CurrentLayer = false;
    var state = false;
    var viewWidth = "thirteen";
    var Main = false;
    var toolbarUI = {};

    // data funcs

    function archive_obj(obj, onArchived) {
      sb.dom.alerts.ask(
        {
          title: "Send to archive?",
          text: "You can always un-archive this later.",
        },
        function (response) {
          if (response) {
            swal.close();

            sb.data.db.obj.erase(obj.object_bp_type, obj.id, onArchived);
          }
        }
      );
    }

    function template_obj(obj, onTemplateCreated) {
      var updTemplate = {
        id: obj.id,
        is_template: !!obj.is_template ? 0 : 1,
      };

      sb.data.db.obj.update(obj.object_bp_type, updTemplate, function (res) {
        if (res) onTemplateCreated(res);
      });
    }

    // new navigation functions
    function buildLeftNav(ui, state) {
      top_nav(ui, false);
      build_mainSystemBar(ui, state);
    }

    function buildRightTray(ui, layers, forceRefresh, appViews, newAppViews) {
      function buildRightTrayBoxViews(dom, state) {
        sb.notify({
          type: "show-dashboard",
          data: {
            dom: dom,
            state: state,
            draw: function (data) {
              data.after(dom);
            },
            appViews: appViews,
            newAppViews: newAppViews,
          },
        });
      }

      function getPageObjectType(state) {
        // Grab the page object type
        var pageObjectType = state.pageObject.group_type
          ? state.pageObject.group_type
          : state.pageObject.object_bp_type;

        // Translate page object type
        if (pageObjectType === "myStuff") {
          pageObjectType = "My Stuff";
        } else if (pageObjectType === "contacts") {
          pageObjectType = "Contact";
        } else if (pageObjectType === "companies") {
          pageObjectType = "Company";
        } else if (pageObjectType === "Project") {
          pageObjectType = "Project";
        } else if (pageObjectType === "Team") {
          pageObjectType = "Team";
        } else if (pageObjectType === "Headquarters") {
          pageObjectType = "HQ";
        }

        return pageObjectType;
      }

      // Define containers
      var contextualRightTrayContainer = ui.contextualRightTrayContainer;
      var myStuffRightTrayContainer = ui.myStuffRightTrayContainer;

      // Start counter
      var count =
        appConfig.is_portal && appConfig.instance === "foundation_group"
          ? 1
          : layers.length;
      var i = 1;

      // Contextual Trays
      _.each(layers, function (state) {
        if (i <= count) {
          // Make sure headquarters is included properly
          if (state.id === "headquarters") {
            state.pageObject = appConfig.state.headquarters;

            // Show the company's right tray views if in a portal
          } else if (appConfig.is_portal && appConfig.portal_company) {
            state.pageObject = {
              id: appConfig.portal_company,
              object_bp_type: "companies",
            };
          }

          if (state.pageObject) {
            // Set page objects
            var pageObject = state.pageObject;
            var pageObjectID = state.pageObject.id;
            var pageObjectType = getPageObjectType(state);

            // Determine if the tray container should refresh
            var refreshTray = true;
            if (!forceRefresh) {
              if (cachedRightTrayPageObjects.hasOwnProperty(pageObjectType)) {
                if (cachedRightTrayPageObjects[pageObjectType]) {
                  if (
                    cachedRightTrayPageObjects[pageObjectType].hasOwnProperty(
                      "pageObject"
                    )
                  ) {
                    if (cachedRightTrayPageObjects[pageObjectType].pageObject) {
                      refreshTray = _.isEqual(
                        cachedRightTrayPageObjects[pageObjectType].pageObject,
                        pageObject
                      )
                        ? false
                        : true;
                    }
                  }
                }
              }
            }

            if (refreshTray) {
              if (
                pageObjectType === "HQ" ||
                pageObjectType === "Team" ||
                pageObjectType === "Project" ||
                pageObjectType === "Company" ||
                pageObjectType === "Contact"
              ) {
                if (appConfig.instance === "foundation_group") {
                  if (!appConfig.is_portal) {
                    // Remove right tray section when company type doesn't exists
                    if (!_.findWhere(layers, { pageObjectType: "companies" })) {
                      cachedRightTrayPageObjects = [];
                      contextualRightTrayContainer.empty();
                      contextualRightTrayContainer.patch();
                      return;
                    }

                    // Remove all right-tray sections that aren't company
                    if (pageObjectType !== "Company") {
                      return;
                    }
                  }
                }

                // Create context layout
                var contextualLayoutObj = [];
                var contextualLayoutID = pageObjectID;
                var contextualLayoutName =
                  "rightTray-" +
                  pageObjectType.replace(" ", "") +
                  "-" +
                  sb.dom.randomString(
                    6,
                    "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
                  ) +
                  "-Layout";
                var contextualLayoutObjectSubtypeID = "";
                var contextualLayoutQuery = {
                  layout_id: contextualLayoutID,
                  is_rightTray: 1,
                  paged: {
                    count: true,
                    page: 0,
                    sortCol: "last_updated",
                    sortDir: "desc",
                    sortCast: "date",
                    pageLength: 1,
                  },
                };

                function getPortalCompanyType(state, onComplete) {
                  if (appConfig.is_portal) {
                    sb.data.db.obj.getById(
                      "companies",
                      appConfig.portal_company,
                      function (company) {
                        state.pageObject.type = company.type;
                        onComplete(state);
                      },
                      {
                        type: "id",
                      }
                    );
                  } else {
                    onComplete(state);
                  }
                }

                getPortalCompanyType(state, function (state) {
                  if (state.pageObject.hasOwnProperty("type")) {
                    if (state.pageObject.type) {
                      contextualLayoutObjectSubtypeID =
                        state.pageObject.type.id;
                      contextualLayoutQuery = {
                        is_rightTray: 1,
                        object_type: state.pageObject.object_bp_type,
                        object_subtype: contextualLayoutObjectSubtypeID,
                        paged: {
                          count: true,
                          page: 0,
                          sortCol: "last_updated",
                          sortDir: "desc",
                          sortCast: "date",
                          pageLength: 1,
                        },
                      };
                    }
                  }

                  // ===== DEV TOOL ===== //
                  // sb.data.db.obj.getWhere('layouts', contextualLayoutQuery, function(layout) {
                  // 	sb.data.db.obj.erase('layouts', _.pluck(layout.data, 'id'), function(){});
                  // });
                  // return;
                  // ===== DEV TOOL ===== //

                  if (contextualLayoutID) {
                    var contextualRightTraySubContainer =
                      contextualRightTrayContainer.makeNode(
                        "contextualRightTraySubContainer-" + pageObjectType,
                        "div",
                        {
                          id:
                            "contextualRightTraySubContainer-" + pageObjectType,
                        }
                      );

                    var contextualBoxViewsLabel =
                      contextualRightTraySubContainer.makeNode(
                        "contextualBoxViewsLabel",
                        "div",
                        {
                          text: pageObjectType,
                          style: "font-size:10px; font-weight:bold;",
                        }
                      );
                    var contextualBoxViewsContainer =
                      contextualRightTraySubContainer.makeNode(
                        "contextualBoxViewsContainer",
                        "div",
                        {}
                      );

                    if (
                      sb.permissions.isGroupManager(
                        +sb.data.cookie.userId,
                        pageObject
                      )
                    ) {
                      var contextualAddBtn = contextualRightTraySubContainer
                        .makeNode("contextualAddBtn", "div", {
                          text: '<i class="fas fa-plus"></i>',
                          css: "item rightTrayBoxViewIcon",
                          style:
                            "border-radius: 50% !important; width: 20px; padding: 5px 20px 5px 9px !important; margin: 0 auto; margin-top:10px; background-color: #ddd;",
                        })
                        .notify(
                          "click",
                          {
                            type: "run-method",
                            data: {
                              run: function (data) {
                                // Show app view picker
                                sb.notify({
                                  type: "show-app-view-picker",
                                  data: {
                                    ui: contextualBoxViewsContainer,
                                    state: state,
                                    layout: contextualLayoutObj[0],
                                    callback: function (
                                      layout,
                                      appViews,
                                      newAppViews
                                    ) {
                                      // buildRightTray(ui, layers, false, appViews, newAppViews);
                                    },
                                  },
                                });
                              },
                            },
                          },
                          sb.moduleId
                        );
                    }

                    if (appConfig.instance !== "foundation_group") {
                      var divider = contextualRightTraySubContainer.makeNode(
                        "divider",
                        "div",
                        {
                          text: '<i class="fal fa-horizontal-rule"></i>',
                          style:
                            "margin-top:15px; margin-bottom:15px; color:#cccccc;",
                        }
                      );
                    }

                    function getLayouts(contextualLayoutQuery, onComplete) {
                      sb.data.db.obj.getWhere(
                        "layouts",
                        contextualLayoutQuery,
                        function (layout) {
                          onComplete(layout);
                        }
                      );
                    }

                    getLayouts(contextualLayoutQuery, function (layout) {
                      // Assign to layout object
                      contextualLayoutObj = layout.data ? layout.data : [];

                      if (!contextualLayoutObj.length) {
                        var newLayout = {
                          layout_id: contextualLayoutID,
                          name: contextualLayoutName,
                          box_links: 1,
                          is_rightTray: 1,
                          group_type: state.pageObject.group_type,
                          object_type: state.pageObject.object_bp_type,
                          object_subtype: contextualLayoutObjectSubtypeID,
                          pageObject: state.pageObject,
                          layout: [],
                        };

                        sb.data.db.obj.create(
                          "layouts",
                          newLayout,
                          function (layout) {
                            contextualLayoutObj.push(layout);
                            buildRightTrayBoxViews(
                              contextualBoxViewsContainer,
                              contextualLayoutObj[0]
                            );
                          }
                        );
                      } else {
                        buildRightTrayBoxViews(
                          contextualBoxViewsContainer,
                          contextualLayoutObj[0]
                        );
                      }

                      // Grab the active selected app view icon
                      var activeBoxViewID = $(
                        ".rightTrayBoxViewIcon.active"
                      ).attr("id");

                      // Patch the UI
                      ui.patch();

                      // Set the icon active again after patch
                      $("#" + activeBoxViewID).addClass("active");

                      // Update state cache
                      cachedRightTrayPageObjects[pageObjectType] = {
                        layout: contextualLayoutObj[0],
                        pageObject: pageObject,
                      };

                      // Append container to maintain sort order
                      $("#contextualRightTrayContainer").append(
                        $("#contextualRightTraySubContainer-" + pageObjectType)
                      );
                    });
                  }
                });
              }
            }
          }
        }

        // Remove tray containers that aren't needed
        if (i === count) {
          // Initialize a temp object
          var newCachedRightTrayPageObjects = {};

          // Loop through and create an array to compare the cache to
          _.each(layers, function (state) {
            // Make sure headquarters is included properly
            if (state.id === "headquarters") {
              state.pageObject = appConfig.state.headquarters;
            }

            if (state.pageObject) {
              // Get page object type
              var pageObjectType = getPageObjectType(state);

              // Push a copy of the cache to a new temp object for comparison
              newCachedRightTrayPageObjects[pageObjectType] =
                cachedRightTrayPageObjects[pageObjectType];
            }
          });

          var cachedRightTrayPageObjectKeys = _.keys(
            cachedRightTrayPageObjects
          );
          // Loop through and remove tray containers if they don't exist anymore
          _.each(cachedRightTrayPageObjectKeys, function (pageObjectType) {
            if (
              pageObjectType !== "MyStuff" &&
              !newCachedRightTrayPageObjects[pageObjectType]
            ) {
              cachedRightTrayPageObjects[pageObjectType] = {};

              if (
                contextualRightTrayContainer[
                  "contextualRightTraySubContainer-" + pageObjectType
                ]
              ) {
                contextualRightTrayContainer[
                  "contextualRightTraySubContainer-" + pageObjectType
                ].empty();
                ui.patch();
              }
            }
          });
        }

        // Iterate counter
        i++;
      });

      // My Stuff Tray
      if (false) {
        if (appConfig.instance !== "foundation_group") {
          sb.data.db.obj.getById(
            "users",
            sb.data.cookie.get("uid"),
            function (user) {
              sb.data.db.obj.getWhere(
                "groups",
                { user: user.id, group_type: "MyStuff" },
                function (myStuff) {
                  // Determine state
                  var myStuff = myStuff[0];
                  var rightTrayState = _.clone(state);
                  rightTrayState.myStuff = myStuff;

                  // Determine page object
                  var pageObject = myStuff;
                  var pageObjectID = myStuff.id;
                  var pageObjectType = myStuff.group_type;

                  // Determine if the tray container should refresh
                  var refreshTray = true;
                  if (!forceRefresh) {
                    if (
                      cachedRightTrayPageObjects.hasOwnProperty(pageObjectType)
                    ) {
                      if (cachedRightTrayPageObjects[pageObjectType]) {
                        if (
                          cachedRightTrayPageObjects[
                            pageObjectType
                          ].hasOwnProperty("pageObject")
                        ) {
                          if (
                            cachedRightTrayPageObjects[pageObjectType]
                              .pageObject
                          ) {
                            refreshTray = _.isEqual(
                              cachedRightTrayPageObjects[pageObjectType]
                                .pageObject,
                              pageObject
                            )
                              ? false
                              : true;
                            if (appConfig.is_portal) {
                              refreshTray = false;
                            }
                          }
                        }
                      }
                    }
                  }

                  if (refreshTray) {
                    var myStuffBoxViewsLabel =
                      myStuffRightTrayContainer.makeNode(
                        "myStuffBoxViewsLabel",
                        "div",
                        {
                          text: "My Stuff",
                          style: "font-size:10px; font-weight:bold;",
                        }
                      );

                    var myStuffBoxViewsContainer =
                      myStuffRightTrayContainer.makeNode(
                        "myStuffBoxViewsContainer",
                        "div",
                        {}
                      );

                    var myStuffAddBtn = myStuffRightTrayContainer
                      .makeNode("myStuffAddBtn", "div", {
                        text: '<i class="fas fa-plus"></i>',
                        css: "item rightTrayBoxViewIcon",
                        style:
                          "border-radius: 50% !important; width: 20px; padding: 5px 20px 5px 9px !important; margin: 0 auto; margin-top:10px; background-color: #ddd;",
                      })
                      .notify(
                        "click",
                        {
                          type: "run-method",
                          data: {
                            run: function (data) {
                              // Show app view picker
                              sb.notify({
                                type: "show-app-view-picker",
                                data: {
                                  ui: myStuffBoxViewsContainer,
                                  state: state,
                                  layout: myStuffLayoutObj[0],
                                  callback: function (
                                    layout,
                                    appViews,
                                    newAppViews
                                  ) {
                                    buildRightTray(
                                      ui,
                                      layers,
                                      true,
                                      appViews,
                                      newAppViews
                                    );
                                  },
                                },
                              });
                            },
                          },
                        },
                        sb.moduleId
                      );

                    // Create My Stuff layout
                    var myStuffLayoutObj;
                    var myStuffLayoutID = rightTrayState.hasOwnProperty(
                      "myStuff"
                    )
                      ? rightTrayState.myStuff.id
                      : "";

                    if (myStuffLayoutID) {
                      // ===== DEV TOOL ===== //
                      // sb.data.db.obj.getWhere('layouts', {layout_id: myStuffLayoutID}, function(layout) {
                      // 	sb.data.db.obj.erase('layouts', _.pluck(layout, 'id'), function(){});
                      // });
                      // return;
                      // ===== DEV TOOL ===== //

                      var where = {
                        layout_id: myStuffLayoutID,
                        is_rightTray: 1,
                        paged: {
                          count: true,
                          page: 0,
                          sortCol: "last_updated",
                          sortDir: "desc",
                          sortCast: "date",
                          pageLength: 1,
                        },
                      };

                      sb.data.db.obj.getWhere(
                        "layouts",
                        where,
                        function (layout) {
                          // Assign to layout object
                          myStuffLayoutObj = layout.data ? layout.data : [];

                          if (!myStuffLayoutObj.length) {
                            var newLayout = {
                              layout_id: myStuffLayoutID,
                              name: "rightTray-MyStuff-Layout",
                              box_links: 1,
                              is_rightTray: 1,
                              group_type: "MyStuff",
                              myStuff: myStuff,
                              layout: [],
                            };

                            sb.data.db.obj.create(
                              "layouts",
                              newLayout,
                              function (layout) {
                                myStuffLayoutObj.push(layout);
                                buildRightTrayBoxViews(
                                  myStuffBoxViewsContainer,
                                  myStuffLayoutObj[0]
                                );
                              }
                            );
                          } else {
                            buildRightTrayBoxViews(
                              myStuffBoxViewsContainer,
                              myStuffLayoutObj[0]
                            );
                          }

                          // Grab the active selected app view icon
                          var activeBoxViewID = $(
                            ".rightTrayBoxViewIcon.active"
                          ).attr("id");

                          // Patch the UI
                          ui.patch();

                          // Set the icon active again after patch
                          $("#" + activeBoxViewID).addClass("active");

                          // Update state cache
                          cachedRightTrayPageObjects[pageObjectType] = {
                            layout: myStuffLayoutObj[0],
                            pageObject: pageObject,
                          };
                        }
                      );
                    }
                  }
                }
              );
            }
          );
        }
      }

      // Open right tray if it is already open
      if (rightTrayOpen === true) {
        sb.notify({
          type: "show-app-view-in-right-tray",
        });
      }

      // Cache layers
      cachedLayers = layers;
    }

    // view funcs
    function top_nav(ui, mobileMenu) {
      TopNav = ui;

      if (isAdmin) {
        numItems = "four";
      } else {
        if (currentUser.type) {
          if (currentUser.type === "admin" || currentUser.type === "Admin") {
            numItems = "four";
          }
        }
      }

      ui.makeNode("modals", "div", {});

      var hqActiveState = "";
      var portalsActiveState = "";
      var myStuffActiveState = "";
      var teamsActiveState = "";
      var settingsActiveState = "";

      switch (CurrentLayer.id) {
        case "headquarters":
          hqActiveState = "active ";
          break;

        case "portals":
          portalsActiveState = "active ";
          break;

        case "my-stuff":
          myStuffActiveState = "active ";
          break;

        case "mystuff-tools1":
          teamsActiveState = "active ";
          break;

        case "admin":
          settingsActiveState = "active ";
          break;
      }

      var topNav = ui.makeNode("topNav", "div", {
        css: "ui vertical labeled icon fluid borderless secondary menu mainNavigation",
        style: "margin-left:0px !important; margin-top:0px !important;",
      });

      //ui.navCol.nav.seg.navigation.makeNode('lb_1', 'lineBreak', {spaces: 1});

      if (appConfig.is_portal) {
        return;
      }

      /*
			  if (
				  !_.isEmpty(appConfig.headquarters)
				  && !_.isEmpty(appConfig.headquarters.name)
			  ) {

				  ui.navCol.nav.seg.navigation.makeNode('hqTitle', 'div', {
					  css: 'ui mini grey header item'
					  , tag: 'h5'
					  , text: appConfig.headquarters.name.toUpperCase()
				  });

			  }
  */

      // Set variables
      var isManager = sb.permissions.isGroupManager(
        +sb.data.cookie.userId,
        appConfig.headquarters
      );

      if (isManager) {
        topNav
          .makeNode("home", "div", {
            text: "",
            css: hqActiveState + "item main-left-nav-item",
            style: "padding-bottom:5px; top:65px;",
            tag: "a",
            href: window.location.href.split("#")[0] + "#hq",
            walkthrough: {
              name: "welcome",
              step: "02-headquarters",
            },
          })
          .makeNode("icon", "div", {
            css: "building icon",
            tag: "i",
            listener: {
              type: "popup",
              hoverable: true,
              delay: {
                show: 500,
                hide: 0,
              },
            },
            tooltip: {
              title: "HQ",
              text: "",
              position: "right center",
            },
          });
      }

      if (!_.isEmpty(Portals)) {
        var marginTop = "top:65px;";
        if (isManager) {
          marginTop = "top:111px;";
        }
        topNav.makeNode("portals", "div", {
          text: "",
          css:
            portalsActiveState +
            " ui item main-left-nav-item left pointing dropdown",
          style: "padding-bottom:5px;" + marginTop,
          tag: "a",
        });

        topNav.portals.makeNode("icon", "div", {
          css: "street view icon",
          style: "font-size:2.1em !important;",
          tag: "i",
          listener: {
            type: "popup",
            hoverable: true,
            delay: {
              show: 500,
              hide: 0,
            },
          },
          // tooltip:{
          // 	title: 'Portals',
          // 	text: '',
          // 	position: 'right center'
          // }
        });

        var portalMenu = topNav.portals.makeNode("menu", "div", {
          css: "inverted menu",
        });

        // Portals to other instances
        _.each(Portals, function (portal) {
          var portalLink = portalMenu
            .makeNode("item-" + portal.id, "div", {
              css: "ui basic item",
              style: "font-size:1.2em !important;",
              text:
                '<i class="ui external link icon"></i> ' + portal.systemName,
            })
            .notify("click", {
              type: "run-method",
              data: {
                run: function () {
                  window.location.href =
                    window.location.href.split("#")[0] + "#p-" + portal.id;
                },
              },
            });
        });
      }

      var marginTop = "top:65px;";
      if (isManager && !_.isEmpty(Portals)) {
        marginTop = "top:157px;";
      } else if (isManager || !_.isEmpty(Portals)) {
        marginTop = "top:111px;";
      }
      topNav
        .makeNode("mystuff", "div", {
          css: myStuffActiveState + "item main-left-nav-item",
          style: "padding-bottom:5px;" + marginTop,
          text: "",
          tag: "a",
          href: window.location.href.split("#")[0] + "#mystuff",
        })
        .makeNode("icon", "div", {
          css: "home icon",
          tag: "i",
          listener: {
            type: "popup",
            hoverable: true,
            delay: {
              show: 500,
              hide: 0,
            },
          },
          tooltip: {
            title: "My Stuff",
            text: "",
            position: "right center",
          },
        });

      var marginTop = "top:111px;";
      if (isManager && !_.isEmpty(Portals)) {
        marginTop = "top:208px;";
      } else if (isManager || !_.isEmpty(Portals)) {
        marginTop = "top:157px;";
      }
      topNav
        .makeNode("teams", "div", {
          tag: "a",
          css: teamsActiveState + "item main-left-nav-item",
          style: "padding-bottom:5px;" + marginTop,
          text: "",
          href: window.location.href.split("#")[0] + "#mystuff&1=mst-myteams",
        })
        .makeNode("icon", "div", {
          css: "users icon",
          tag: "i",
          listener: {
            type: "popup",
            hoverable: true,
            delay: {
              show: 500,
              hide: 0,
            },
          },
          tooltip: {
            title: "Teams",
            text: "",
            position: "right center",
          },
        });

      /*
			  ui.navCol.nav.seg.navigation.makeNode('teamsItem', 'div', {
				  css: 'item main-left-nav-item'
				  , style: 'margin-left: 15px !important; padding-top: 0px !important;'
			  });

			  ui.navCol.nav.seg.navigation.teamsItem.makeNode('menu', 'div', {
				  css: 'menu'
			  });

			  ui.navCol.nav.seg.navigation.teamsItem.menu.makeNode('loaderWrap', 'div', {
				  css: 'text-center'
			  });
			  ui.navCol.nav.seg.navigation.teamsItem.menu.loaderWrap.makeNode('loader', 'loader', {});
			  ui.navCol.nav.seg.navigation.teamsItem.menu.loaderWrap.makeNode('loaderText', 'div', {
				  text: 'Fetching teams...'
			  });
  */

      /*
			  sb.data.db.obj.getWhere('groups', {
				  group_type: 'Team'
				  , tagged_with: [+sb.data.cookie.userId]
				  , childObjs: {
					  name: true
				  }
			  }, function(teamsList) {

				  teamsList = _.sortBy(teamsList, 'name');

				  ui.navCol.nav.seg.navigation.teamsItem.menu.empty();

				  if(teamsList) {

					  if(!_.isEmpty(teamsList)) {

						  var moreTeams = [];
						  var currentTeamId = undefined;

						  if(!_.isEmpty(urlData)) {

							  currentTeamId = parseInt(urlData[Object.keys(urlData)[0]].split('-')[2]);

						  }

						  _.each(teamsList, function(team, i) {

							  if(i <= 9) {

								  var active = '';

								  if(currentTeamId !== undefined
									  && currentTeamId === team.id) {

										  active = 'active';

									  }

								  ui.navCol.nav.seg.navigation.teamsItem.menu.makeNode('team'+team.id, 'div', {
									  tag: 'a'
									  , text: team.name
									  , css: 'item ' + active
									  , href: sb.data.url.createPageURL('object-view', {
										  id: 		team.id
										  , name: 	team.name
										  , type: 	'team'
										  , startAt: 	window.location.href.split('#')[0] +'#mystuff'
									  })
								  });


							  } else {

								  moreTeams.push(team);

							  }


						  });

						  if(!_.isEmpty(moreTeams)) {

							  var teamList = [];

							  _.each(moreTeams, function(team) {

								  teamList.push({
									  name: team.name
									  , value: team.id
								  });

							  });


							  ui.navCol.nav.seg.navigation.teamsItem.menu.makeNode('more', 'div', {
								  text: moreTeams.length + ' more <i class="dropdown left icon"></i>'
								  , css: 'ui right dropdown item'
								  , listener: {
									  type:'dropdown'
									  , values: teamList
									  , style: 'left: 50% !important;'
									  , onChange: function(value, text) {

										  if(value) {

											  window.location.href = sb.data.url.createPageURL('object-view', {
												  id: 		+value
												  , name: 	text
												  , type: 	'team'
												  , startAt: 	window.location.href.split('#')[0] +'#mystuff'
											  });

										  }

									  }
								  }
							  });

						  }

					  } else {

						  ui.navCol.nav.seg.navigation.teamsItem.menu.makeNode('noTeams', 'div', {
							  css: 'text-center text-muted'
							  , text: 'No teams found'
						  });

					  }

				  } else {

					  console.log('Failed to load teams list in left nav');

				  }

				  ui.navCol.nav.seg.navigation.teamsItem.menu.patch();

			  });
  */

      if (isManager) {
        /*
				  if( rootPage === 'team' ){ cssString = 'active '; }else{ cssString = ''; }
				  ui.navCol.nav.seg.navigation.makeNode('team', 'div', {
						  text:'Team Members',
						  css:cssString +'item',
						  tag:'a',
						  href:sb.data.url.createPageURL(
							  'hqTool',
							  {
								  tool:'staffingTool'
							  }
						  )
					  }).makeNode('icon', 'div', {css:'users icon', tag:'i'});
  */

        // !Pinned button
        // var marginTop = 'top:111px;';
        // if ( isManager && !_.isEmpty(Portals) ) {
        // 	marginTop = 'top:258px;';
        // } else if ( isManager || !_.isEmpty(Portals) ) {
        // 	marginTop = 'top:203px;';
        // }
        // topNav.makeNode('pinned', 'div', {
        // 	text: '',
        // 	css: 'ui item main-left-nav-item left pointing dropdown',
        // 	style: 'padding-bottom:5px;' + marginTop,
        // 	tag: 'a'
        // });

        // topNav.pinned.makeNode('icon', 'div', {
        // 	css: 'thumbtack icon',
        // 	style: 'font-size:2.1em !important;',
        // 	tag: 'i',
        // 	listener:{
        // 		type: 'popup',
        // 		hoverable: true,
        // 		delay: {
        // 			show: 500,
        // 			hide: 0
        // 		}
        // 	},
        // 	// tooltip:{
        // 	// 	title: 'Portals',
        // 	// 	text: '',
        // 	// 	position: 'right center'
        // 	// }
        // });

        // var pinnedMenu = topNav.pinned.makeNode('menu', 'div', {
        // 	css:'inverted menu'
        // });

        // // Pinned Items
        // var Pinned = [];
        // _.each(Pinned, function (pinned) {

        // 	var pinnedLink = pinnedMenu.makeNode('item-' + pinned.id, 'div', {
        // 		css: 'ui basic item',
        // 		style: 'font-size:1.2em !important;',
        // 		text: '<i class="ui external link icon"></i> ' + pinned.systemName
        // 	}).notify('click', {
        // 		type:'run-method',
        // 		data:{
        // 			run:function() {
        // 				window.location.href = window.location.href.split('#')[0] +'#p-'+ pinned.id;
        // 			}
        // 		}
        // 	});

        // });

        // !Settings button
        var marginTop = "top:111px;";
        if (isManager && !_.isEmpty(Portals)) {
          marginTop = "top:258px;";
        } else if (isManager || !_.isEmpty(Portals)) {
          marginTop = "top:203px;";
        }
        // var marginTop = 'top:111px;';
        // if ( isManager && !_.isEmpty(Portals) ) {
        // 	marginTop = 'top:258px;';
        // } else if ( isManager || !_.isEmpty(Portals) ) {
        // 	marginTop = 'top:261px;';
        // }
        topNav
          .makeNode("settings", "div", {
            text: "",
            css: settingsActiveState + " item main-left-nav-item",
            style: "padding-bottom:5px;" + marginTop,
            tag: "a",
            href: window.location.href.split("#")[0] + "#settings",
          })
          .makeNode("icon", "div", {
            css: "cogs icon",
            tag: "i",
            listener: {
              type: "popup",
              hoverable: true,
              delay: {
                show: 500,
                hide: 0,
              },
            },
            tooltip: {
              title: "Settings",
              text: "",
              position: "right center",
            },
          });
      }
    }

    function main_ui(ui, layers, state) {
      $("body").append(
        '<div id="loader" class="ui active dimmer inverted" style="display:none;">' +
          '<div class="ui text loader">Loading</div>' +
          "</div>"
      );

      function refreshPageBody() {
        load_view(currentView, state);
      }

      function pageHeaderView(container, state, currentView) {
        function menu_ui(ui, pageObj, currentView) {
          function menu_btn_ui(ui, btn, refresh) {
            var icon = btn.icon;
            var title = btn.title;

            if (typeof btn.icon == "function") {
              icon = btn.icon(pageObj);
            }

            if (typeof btn.title == "function") {
              title = btn.title(pageObj);
            }

            // Hide adminOnly buttons from non-admins
            if (
              btn.adminOnly &&
              appConfig.user.type !== "admin" &&
              appConfig.user.type !== "developer"
            ) {
              return;
            }

            ui.makeNode(btn.name, "div", {
              css: "item",
              text: '<i class="' + icon + ' icon"></i> ' + title,
              style: "width:100%;",
            }).notify(
              "click",
              {
                type: "run-method",
                data: {
                  run: function (btn, pageObj) {
                    btn.action(pageObj, btn);
                  }.bind(null, btn, pageObj),
                },
              },
              sb.moduleId
            );

            btn.update = function (icon, title) {
              $(ui[btn.name].selector).html(
                '<i class="' + icon + ' icon"></i> ' + title
              );
            };

            if (refresh) ui.patch();

            if (typeof btn.draw === "function") {
              btn.draw(
                pageObj,
                function (selector, icon, title) {
                  $(selector).html(
                    '<i class="' + icon + ' icon"></i> ' + title
                  );
                }.bind({}, ui[btn.name].selector)
              );
            }
          }

          var btns = [];
          /* 					var menu = ui; */
          /*
					  var isManager = _.chain(pageObj.write).contains( +sb.data.cookie.userId ).value();
  console.log('isManager', isManager);
					  if(isManager !== true){
						  return;
					  }
  */
          if (currentView.actions) {
            ui.makeNode("mainMenu", "div", { css: "" }).makeNode(
              "right",
              "div",
              { css: "pull-right", style: "padding-bottom:0;" }
            );
            ui.patch();
            sb.notify({
              type: "view-field",
              data: {
                type: "actions-set",
                property: "state",
                obj: pageObj,
                options: {
                  actions: currentView.actions,
                },
                ui: ui.mainMenu.right,
              },
            });
          } else if (currentView.header.menu) {
            _.each(currentView.header.menu, function (action, key) {
              var title = action.title || "";
              var icon = action.icon || "";

              if (key === "edit") {
                title = "Edit";
                icon = "orange edit";
              }

              if (key === "edit" && typeof action === "function") {
                btns.push({
                  name: "edit",
                  icon: "orange edit",
                  title: "Edit",
                  action: function () {
                    EditModal.body.empty();
                    EditModal.show();

                    currentView.header.menu.edit(
                      pageObj,
                      EditModal.body,
                      state,
                      function (response) {
                        // !TODO: reload page better
                        EditModal.hide();
                        location.reload();
                      }
                    );
                  },
                });
              } else if (key === "followUnfollow") {
                var followIcon = function (obj) {
                  var icon = "bell outline yellow icon";

                  if (
                    obj.notify &&
                    _.contains(obj.notify, +sb.data.cookie.userId)
                  ) {
                    icon = "bell yellow icon";
                  }

                  return icon;
                };
                var followTitle = function (obj) {
                  var title = "Follow";

                  if (
                    obj.notify &&
                    _.contains(obj.notify, +sb.data.cookie.userId)
                  ) {
                    title = "Unfollow";
                  }

                  return title;
                };

                btns.push({
                  name: "followUnfollow",
                  icon: followIcon,
                  title: followTitle,
                  ui: false,
                  action: function (obj, dom, state, draw) {
                    updateNotifyList(obj, null, function (response) {
                      obj.notify = response.notify;

                      dom.update(followIcon(response), followTitle(response));

                      menu_ui.refresh(response, currentView);
                    });
                  },
                });
              } else if (
                typeof action === "object" &&
                typeof action.action === "function"
              ) {
                defaultAction = function (menu, btnObj) {
                  var actionUI;

                  if (action.ui === false) {
                    actionUI = menu;
                  } else {
                    actionUI = EditModal;

                    if (EditModal.hasOwnProperty("body")) {
                      EditModal.body.empty();
                    }

                    EditModal.show();
                  }

                  action.action(
                    pageObj,
                    actionUI,
                    state,
                    function (response) {
                      if (action.ui != false) {
                        EditModal.hide();
                        location.reload();
                      }

                      refresh_page();

                      // !TODO: reload page better
                      // 									location.reload();
                    },
                    btnObj
                  );
                };

                if (action.fullView) {
                  defaultAction = function (btnObj) {
                    action.action(
                      pageObj,
                      Main,
                      state,
                      function (response) {
                        refresh_page();
                      },
                      btnObj
                    );
                  };
                }

                btns.push({
                  name: key,
                  adminOnly: action.adminOnly || false,
                  icon: icon,
                  title: title,
                  action: defaultAction,
                  draw: action.draw || false,
                });
              }
            });

            if (currentView.header.menu.archive !== false) {
              btns.push({
                name: "archive",
                icon: "red archive",
                title: "Archive",
                action: function (obj) {
                  archive_obj(obj, function (response) {
                    if (response) {
                      var url = sb.data.url.createPageURL("UP");
                      window.location.href = url;
                    }
                  });
                }.bind({}, pageObj),
              });
            }

            ui.makeNode("mainMenu", "div", {
              css: "ui borderless menu",
              style: "margin-bottom:12px;",
            }).makeNode("right", "div", { css: "right menu" });

            if (currentView.header.actions) {
              sb.notify({
                type: "show-actions-menu",
                data: {
                  ui: ui.mainMenu.right,
                  actions: currentView.header.actions,
                  obj: pageObj,
                },
              });
            } else {
              // if (sb.permissions.isGroupManager(+sb.data.cookie.userId, pageObj)) {

              ui.mainMenu.right.makeNode("edit", "div", {
                text: 'Options <i class="ui dropdown icon"></i>',
                css: "ui simple dropdown item",
              });

              var menu = ui.mainMenu.right.edit.makeNode("menu", "div", {
                css: "menu",
              });

              if (currentView.header.menu.templates === false) {
              } else {
                var templateIcon = function (obj) {
                  var icon = "window restore icon";

                  if (obj)
                    if (obj.is_template == 1)
                      icon = "window restore outline icon";

                  return icon;
                };

                var templateText = function (obj) {
                  var text =
                    '<span style="color:gray;">Move to Templates</span>';
                  if (obj)
                    if (obj.is_template == 1) {
                      text = "Remove from Templates";
                    }
                  return text;
                };

                btns.push({
                  name: "template",
                  icon: templateIcon,
                  title: templateText,
                  action: function (obj, dom) {
                    template_obj(obj, function (response) {
                      pageObj.is_template = response.is_template;

                      dom.update(
                        templateIcon(response),
                        templateText(response)
                      );

                      menu_ui.refresh(response, currentView);
                    });
                  },
                });
              }

              _.each(btns, function (btn) {
                menu_btn_ui(menu, btn);
              });

              // }
            }
          }

          if (pageObj) {
            if (
              pageObj.notify &&
              _.contains(pageObj.notify, +sb.data.cookie.userId)
            ) {
              ui.makeNode("noti", "div", {
                listener: {
                  type: "popup",
                  hoverable: true,
                },
                tooltip: {
                  title: "You are notifications enabled.",
                  text: "Use the Options menu to the right to quickly follow or unfollow any updates.",
                  position: "left center",
                },
                text: '<i class="bell icon"></i>',
                css: "section right floated",
                style:
                  "float:right; cursor:pointer; padding-top:14px; margin-left:16px;",
              });
            } else {
              if (ui.noti) delete ui.noti;
            }

            if (pageObj.is_template) {
              ui.makeNode("template", "div", {
                listener: {
                  type: "popup",
                  hoverable: true,
                },
                tooltip: {
                  title: "This item is currently a Template",
                  text: "Use the Options menu to the right to quickly toggle the status.",
                  position: "left center",
                },
                text: '<i class="window restore icon"></i>',
                css: "section right floated",
                style:
                  "float:right; cursor:pointer; padding-top:14px; margin-left:16px;",
              });
            } else {
              if (ui.template) delete ui.template;
            }
          }

          ui.patch();
        }

        function headerTags(currentView) {
          // tags
          if (currentView.header.tags) {
            if (Components.pageTag.hasOwnProperty("destroy")) {
              Components.pageTag.destroy();
            }

            Components.pageTag = sb.createComponent("tags");

            ui.grid.makeNode("tagsContainer", "div", {
              css: "sixteen wide column",
              style: "background-color: white !important;",
            });

            ui.grid.tagsContainer.makeNode("tags", "div", {});

            ui.grid.patch();

            // tags
            sb.notify({
              type: "view-field",
              data: {
                type: "tags",
                property: "tagged_with",
                obj: pageObj,
                options: {},
                ui: ui.grid.tagsContainer.tags,
              },
            });
          }
        }

        if (!state.pageObject && state.headquarters) {
          state.pageObject = state.headquarters;
        }

        var headerStyle = "margin-bottom:30px;";

        if (state.hasOwnProperty("pageObject")) {
          if (state.pageObject.hasOwnProperty("group_type")) {
            if (
              state.pageObject.group_type == "Project" ||
              state.pageObject.group_type == "Team"
            ) {
              headerStyle = "margin-bottom:0px;";
            }
          } else if (state.pageObject.hasOwnProperty("object_bp_type")) {
            if (state.pageObject.object_bp_type) {
              headerStyle = "margin-bottom:0px;";
            }
          }
        }

        if (!currentView.hasOwnProperty("header")) {
          headerStyle = "display:none;";
        }

        var ui = container.makeNode("Header", "div", {
          css: "",
          style: headerStyle,
        });
        var pageObj = state.pageObject;
        var titleOptions = {
          edit: true,
          tag: "h1",
          link: false,
        };
        var detailOptions = {
          edit: true,
          editing: true,
        };

        if (pageObj.data_source_id && !is_portal) {
          ui.makeNode("templateLabel", "div", {});



          sb.data.db.obj.getById(
            "",
            pageObj.data_source_id,
            function (template) {

              var labelText = 'Generated from a template';

              if ( template && template.hasOwnProperty('name') ){
                    labelText = "Generated from: " + template.name;
              }

              ui.templateLabel.makeNode("templateName", "div", {
                text: labelText,
                css: "ui label",
              });

              ui.patch();
            }
          );
        }

        container.patch();

        if (typeof currentView === "undefined") {
          ui.makeNode("loader", "div", { css: "ui loading container" });
          return;
        }

        if (!_.isEmpty(currentView) && !_.isEmpty(currentView.header)) {
          var canEdit = true;

          if (typeof currentView.header.canEdit === "function") {
            canEdit = currentView.header.canEdit(
              +sb.data.cookie.userId,
              pageObj
            );
          }

          if (is_portal) {
            canEdit = false;
          }

          /*
  if ( isAdmin ) {
						  canEdit = true;
					  }
  */

          ui.makeNode("grid", "div", { css: "ui stackable grid" });

          ui.grid.makeNode("left", "div", {
            css: "eight wide column",
            style: "padding-bottom:0;",
          });
          ui.grid.left.makeNode("row1", "div", { css: "row" });
          ui.grid.left.row1.makeNode("headerContainer", "div", {
            css: "ui fluid middle aligned container field",
          });

          if (pageObj.profile_image && pageObj.profile_image.id) {
            ui.grid.left.row1.headerContainer.makeNode("prof-img", "div", {
              css: "ui tiny circular spaced image",
              tag: "img",
              style: "margin-right: 12px;",
              src: sb.data.files.getURL(pageObj.profile_image),
            });
          }

          ui.grid.left.makeNode("row3", "div", { css: "row" });
          ui.grid.left.makeNode("row4", "div", { css: "row" });
          ui.grid.left.makeNode("row2", "div", { css: "row" });

          ui.grid.makeNode("right", "div", {
            css: "eight wide column",
            style: "padding-bottom:0;",
          });
          ui.grid.right.makeNode("row0", "div", { css: "row" });
          ui.grid.right.makeNode("row1", "div", { css: "row" });
          ui.grid.right.makeNode("row2", "div", {
            css: "row",
            style: "padding:6px;",
          });

          // menu
          if (canEdit) {
            menu_ui(ui.grid.right.row0, pageObj, currentView);
            menu_ui.refresh = menu_ui.bind(null, ui.grid.labels);
          }

          // header (name)
          if (currentView.header.title) {
            if (_.isObject(currentView.header.title.options)) {
              _.mapObject(currentView.header.title.options, function (v, k) {
                if (!titleOptions[k]) {
                  return (titleOptions[k] = v);
                }
              });
            }

            titleOptions.edit = canEdit;

            if (
              pageObj.object_bp_type == "contacts" ||
              pageObj.object_bp_type == "users"
            ) {
              ui.grid.left.row1.headerContainer.makeNode("fname", "div", {
                style: "margin:0px; padding:0; display:inline-block",
              });
              ui.grid.left.row1.headerContainer.makeNode("space", "div", {
                style: "margin: 0px 2px; display:inline-block",
                text: "&nbsp",
              });
              ui.grid.left.row1.headerContainer.makeNode("lname", "div", {
                style: "margin:0px; padding:0; display:inline-block",
              });

              sb.notify({
                type: "view-field",
                data: {
                  type: "title",
                  property: "fname",
                  ui: ui.grid.left.row1.headerContainer.fname,
                  obj: pageObj,
                  options: titleOptions,
                },
              });

              sb.notify({
                type: "view-field",
                data: {
                  type: "title",
                  property: "lname",
                  ui: ui.grid.left.row1.headerContainer.lname,
                  obj: pageObj,
                  options: titleOptions,
                },
              });
            } else {
              sb.notify({
                type: "view-field",
                data: {
                  type: "title",
                  property: "name",
                  ui: ui.grid.left.row1.headerContainer,
                  obj: pageObj,
                  options: titleOptions,
                },
              });
            }
          } else {
            if (currentView.header.title !== false) {
              titleOptions.edit = sb.permissions.isGroupManager(
                +sb.data.cookie.userId,
                pageObj
              );

              ui.grid.left.row1.headerContainer.makeNode("title", "div", {});

              if (currentView.header.title !== false) {
                sb.notify({
                  type: "view-field",
                  data: {
                    type: "title",
                    property: "name",
                    ui: ui.grid.left.row1.headerContainer.title,
                    obj: pageObj,
                    options: titleOptions,
                  },
                });

                // For projects pages, show the main contact and the company they are a part of
                if (
                  pageObj.object_bp_type === "groups" &&
                  pageObj.group_type === "Project" &&
                  // Header area, showing the project name, client name, and company name
                  pageObj.main_contact
                ) {
                  ui.grid.left.row1.headerContainer
                    .makeNode("subheader", "div", {})
                    .makeNode("loader", "div", {
                      text: '<i class="notched circle loading icon"></i>',
                      style: "width:100%;height:100%;text-align:center;",
                    });

                  sb.data.db.obj.getById(
                    "companies",
                    pageObj.main_contact.company,
                    function (company) {
                      var contactLink = sb.data.url.createPageURL("object", {
                        name: pageObj.main_contact.name,
                        type: "contacts",
                        id: pageObj.main_contact.id,
                      });
                      var companyLink = sb.data.url.createPageURL("object", {
                        name: company.name,
                        type: "companies",
                        id: company.id,
                      });

                      var companyTxt =
                        " at " +
                        '<a class="ui link" href="' +
                        companyLink +
                        '">' +
                        company.name +
                        "</a>";

                      var txt =
                        '<div class="sub header">for ' +
                        '<a class="ui link" href="' +
                        contactLink +
                        '">' +
                        pageObj.main_contact.name +
                        "</a>" +
                        companyTxt;
                      ("</div>");

                      ui.grid.left.row1.headerContainer.subheader.empty();
                      ui.grid.left.row1.headerContainer.subheader.makeNode(
                        "h",
                        "div",
                        {
                          tag: "h1",
                          css: "ui header",
                          text: txt,
                        }
                      );
                      ui.grid.left.row1.headerContainer.subheader.patch();
                    },
                    {
                      name: true,
                    }
                  );
                }
              } else {
                delete ui.grid.left.row1;
                delete ui.grid.left.row2;
              }
            } else {
              delete ui.grid.left.row1.headerContainer;
            }
          }

          if (currentView.header.detail) {
            if (currentView.header.detail !== false) {
              if (_.isObject(currentView.header.detail.options)) {
                _.each(currentView.header.detail.options, function (v, k) {
                  if (!detailOptions[k]) {
                    return (detailOptions[k] = v);
                  }
                });
              }

              detailOptions.editing = canEdit;

              ui.grid.left.row2.makeNode("detailsContainer", "div", {
                css: "round-border mobile-margin-bottom-15",
                style: "margin-top:30px; padding:1rem;",
              });
              ui.grid.left.row2.detailsContainer.makeNode(
                "detailsHeader",
                "div",
                {
                  text: '<i class="info circle icon"></i> Details',
                  css: "ui grey sub header",
                }
              );
              ui.grid.left.row2.detailsContainer.makeNode("details", "div", {});

              sb.notify({
                type: "view-field",
                data: {
                  type: "detail",
                  property: "description",
                  ui: ui.grid.left.row2.detailsContainer.details,
                  obj: pageObj,
                  options: detailOptions,
                },
              });
            } else {
              delete ui.grid.left.row1;
            }
          }

          //fixed title
          if (
            currentView.header.fixedTitle &&
            typeof currentView.header.fixedTitle === "function"
          ) {
            ui.grid.left.row1.makeNode("fixedTitle", "div", {
              css: "ui fluid middle aligned container field",
            });
            currentView.header.fixedTitle(
              ui.grid.left.row1.fixedTitle,
              pageObj
            );
          }

          //subheader
          if (
            currentView.header.subTitle &&
            typeof currentView.header.subTitle === "function"
          ) {
            ui.grid.left.row2.makeNode("subtitle", "div", {
              css: "ui fluid middle aligned container field",
            });
            currentView.header.subTitle(ui.grid.left.row2.subtitle, pageObj);
          }

          // manager(s)
          if (currentView.header.user && currentView.header.user.field) {
            if (pageObj.hasOwnProperty(currentView.header.user.field)) {
              currentView.header.user.edit = canEdit;

              if (
                currentView.header.user.hasOwnProperty("title") &&
                currentView.header.user.title !== null &&
                currentView.header.user.title != undefined &&
                typeof currentView.header.user.title == "string"
              ) {
                managerHeaderText = currentView.header.user.title;
              } else {
                managerHeaderText = "Manager";
              }

              currentView.header.user.title = "";

              // Managers
              ui.grid.left.row3.makeNode("managersContainer", "div", {
                css: "round-border",
                style:
                  "background-color: white !important; padding:1rem; margin-top:15px;",
              });
              ui.grid.left.row3.managersContainer.makeNode(
                "managersHeader",
                "div",
                {
                  css: "ui grey sub header",
                  style: "margin-bottom:8px;",
                  text: '<i class="users icon"></i> ' + managerHeaderText,
                }
              );
              ui.grid.left.row3.managersContainer.makeNode(
                "managersBody",
                "div",
                {
                  style: "padding-bottom:5px;",
                }
              );
              sb.notify({
                type: "view-field",
                data: {
                  type: "users",
                  property: currentView.header.user.field,
                  obj: pageObj,
                  ui: ui.grid.left.row3.managersContainer.managersBody,
                  options: currentView.header.user,
                },
              });
            }
          } else {
            delete ui.grid.left.row3;
          }

          // type field (label)
          if (currentView.header.type) {
            if (pageObj[currentView.header.type.field] === null) {
              if (pageObj.object_bp_type === "companies") {
                pageObj[currentView.header.type.field] = {
                  object_bp_type: "company_categories",
                };
              } else {
                return;
              }
            }

            var typeOptions = {
              onUpdate: function (updPageObj) {
                state.pageObject = updPageObj;
                pageObj = updPageObj;
                pageHeaderView(container, state, currentView);
                load_view(currentView, state);
              },
            };

            if (_.isObject(currentView.header.type.options)) {
              _.mapObject(currentView.header.type.options, function (v, k) {
                if (!typeOptions[k]) {
                  return (typeOptions[k] = v);
                }
              });
            }

            ui.grid.right.row1.makeNode("typeGrid", "div", {
              css: "ui relaxed grid",
            });
            ui.grid.right.row1.typeGrid.makeNode("left", "div", {
              css: "ui six wide column",
              style: "padding-bottom:0;",
            });
            ui.grid.right.row1.typeGrid.makeNode("right", "div", {
              css: "ui ten wide column",
              style: "padding-bottom:0;",
            });

            // Type
            ui.grid.right.row1.typeGrid.left.makeNode("typeContainer", "div", {
              css: "round-border",
              style:
                "background-color: white !important; padding:1rem; margin-top:15px;",
            });
            ui.grid.right.row1.typeGrid.left.typeContainer.makeNode(
              "typeHeader",
              "div",
              {
                css: "ui grey sub header",
                text: '<i class="folder open icon"></i> Type',
              }
            );
            ui.grid.right.row1.typeGrid.left.typeContainer.makeNode(
              "typeBody",
              "div",
              {
                css: "dropdown-round-border",
                style:
                  "padding-top:0 !important; padding-left:0 !important; padding-bottom:0px !important;",
              }
            );
            sb.notify({
              type: "view-field",
              data: {
                type: "type",
                property: "type",
                obj: pageObj,
                ui: ui.grid.right.row1.typeGrid.left.typeContainer.typeBody,
                options: typeOptions,
              },
            });

            if (pageObj.object_bp_type === "companies") {
              sb.data.db.obj.getById("companies", state.id, function (company) {
                // Value
                ui.grid.right.row1.typeGrid.right.makeNode(
                  "valueContainer",
                  "div",
                  {
                    css: "round-border",
                    style:
                      "background-color: white !important; padding:1rem; margin-top:15px;",
                  }
                );
                ui.grid.right.row1.typeGrid.right.valueContainer.makeNode(
                  "valueHeader",
                  "div",
                  {
                    css: "ui grey sub header",
                    text: '<i class="dollar usd icon"></i> Value',
                  }
                );
                ui.grid.right.row1.typeGrid.right.valueContainer.makeNode(
                  "valueBody",
                  "div",
                  {}
                );
                sb.notify({
                  type: "view-field",
                  data: {
                    type: "currency",
                    property: "value",
                    obj: company,
                    ui: ui.grid.right.row1.typeGrid.right.valueContainer
                      .valueBody,
                    options: {
                      edit: true,
                      editing: true,
                      commitUpdates: true,
                    },
                  },
                });

                // Patch the UI
                ui.grid.right.row1.typeGrid.right.patch();
              });
            }
          }

          // location field
          if (currentView.header.locations) {
            var locOptions = {
              onUpdate: function (updPageObj) {
                state.pageObject = updPageObj;
                pageObj = updPageObj;
                pageHeaderView(container, state, currentView);
                load_view(currentView, state);
              },
            };

            if (_.isObject(currentView.header.locations.options)) {
              _.mapObject(
                currentView.header.locations.options,
                function (v, k) {
                  if (!locOptions[k]) {
                    return (locOptions[k] = v);
                  }
                }
              );
            }

            ui.grid.right.row1.makeNode("locations", "div", {
              css: "ui fluid middle aligned container field",
            });
            sb.notify({
              type: "view-field",
              data: {
                type: "locations",
                property: "locations",
                ui: ui.grid.right.row1.locations,
                obj: pageObj,
                options: locOptions,
              },
            });
          }

          ui.patch();
console.warn('currentView, state', currentView);
          if (currentView.header.startDate) {
            var opt = {
              edit: true,
              dateType: "datetime",
            };

            ui.grid.right.makeNode("dates", "div", {
              css: "ui relaxed stackable grid",
              style: "margin-top:10px;",
            });

            ui.grid.right.dates.makeNode("startDate", "div", {
              css: "ui six wide column",
              style: "padding:0px;",
            });
            ui.grid.right.dates.startDate.makeNode("label", "div", {
              style: "padding-left:6px;",
              css: "ui sub header",
              text: currentView.header.startDate.title + ":",
            });
            ui.grid.right.dates.startDate.makeNode("startField", "div", {
              css: "ui middle aligned container field",
            });

            ui.grid.right.patch();

            sb.notify({
              type: "view-field",
              data: {
                type: "date",
                dateType: "day",
                property: currentView.header.startDate.field,
                ui: ui.grid.right.dates.startDate.startField,
                obj: pageObj,
                options: opt,
              },
            });
          }

          if (currentView.header.endDate) {
            var opt = {
              edit: true,
              dateType: "datetime",
            };

            if (!ui.grid.right.dates) {
              ui.grid.right.makeNode("dates", "div", {
                css: "ui relaxed stackable grid",
              });
            }

            ui.grid.right.dates.makeNode("endDate", "div", {
              css: "ui eight wide column",
              style: "padding:0px;",
            });
            ui.grid.right.dates.endDate.makeNode("label", "div", {
              style: "padding-left:6px;",
              css: "ui sub header",
              text: currentView.header.endDate.title + ":",
            });
            ui.grid.right.dates.endDate.makeNode("endField", "div", {
              css: "ui middle aligned container field",
            });

            ui.grid.right.patch();

            sb.notify({
              type: "view-field",
              data: {
                type: "date",
                dateType: "day",
                property: currentView.header.endDate.field,
                ui: ui.grid.right.dates.endDate.endField,
                obj: pageObj,
                options: opt,
              },
            });
          }

          // state
          if (currentView.header.state) {
            if (pageObj.type && pageObj.type.hasOwnProperty("states")) {
              var statePad = "padding:10px 0px; margin:0px;";

              if ($(window).width() > 768) {
                statePad = "padding:10px 6px; margin:0px;";
              }

              if (!ui.grid.right.row1.typeGrid) {
                ui.grid.right.row1.makeNode("typeGrid", "div", {
                  css: "ui relaxed grid",
                });
                ui.grid.right.row1.typeGrid.makeNode("left", "div", {
                  css: "ui six wide column",
                  style: "padding-bottom:0;",
                });
                ui.grid.right.row1.typeGrid.makeNode("right", "div", {
                  css: "ui ten wide column",
                  style: "padding-bottom:0;",
                });
              }

              ui.grid.right.row1.typeGrid.right.makeNode("state", "div", {
                style: statePad,
                css: "middle aligned row field",
              });
              ui.grid.left.patch();

              sb.notify({
                type: "view-field",
                data: {
                  type: "state",
                  property: "state",
                  ui: ui.grid.right.row1.typeGrid.right.state,
                  obj: pageObj,
                  options: {
                    onUpdate: function (updObj) {
                      load_view(currentView, state);
                    },
                  },
                },
              });
            }
          }

          if (currentView.header.timeTracking) {
            if (!_.isEmpty(currentView.header.timeTracking.label)) {
              ui.grid.right.row2.makeNode("label", "div", {
                text: currentView.header.timeTracking.label,
                css: "ui mini grey sub header",
                tag: "h5",
                style:
                  "line-height:2em;color:rgb(15,15,15) !important; margin-bottom:14px;",
              });
            }

            ui.grid.right.row2.makeNode("time", "div", {
              css: "field",
              style: "margin-bottom:14px;",
            });

            sb.notify({
              type: "view-field",
              data: {
                type: "timeTracking",
                ui: ui.grid.right.row2.time,
                obj: pageObj,
                options: currentView.header.timeTracking,
              },
            });
          }

          headerTags(currentView);
        }

        return;
      }

      if (appConfig.is_portal) is_portal = true;

      ui.empty();

      EditModal = ui.makeNode("modal", "modal", {});

      ui.makeNode("Header", "div", {
        css: "mobilePadding ui basic segment",
      });

      //pageHeaderView(ui, {}, state);

      ui.makeNode("Body", "div", {
        css: "",
        style: "",
      });

      ui.refreshHeader = pageHeaderView.bind({}, ui);
    }

    function compile_layers(appConfig, onComplete) {
      function getObjectView(
        first_param,
        second_param,
        third_param,
        fourth_param
      ) {
        var customParams = {};
        if (!_.isEmpty(fourth_param) && typeof fourth_param === "string") {
          _.each(fourth_param.split("!"), function (param) {
            if (!_.isEmpty(param)) {
              var parameter = param.split(":");
              customParams[parameter[0]] = parseInt(parameter[1]);
            }
          });
        }

        if (
          (second_param !== "c" &&
            second_param !== "e" &&
            isNaN(parseInt(second_param))) ||
          ((second_param === "c" || second_param === "e") &&
            isNaN(parseInt(third_param)))
        ) {
          return {
            icon: "error",
            id: "error-page",
            title: "New",
            views: [ErrorPage],
            url: currentUrl,
            type: "error-msg",
            params: customParams,
          };
        }

        switch (second_param) {
          case "c":
            return {
              icon: "users",
              id: "obj-" + second_param + " " + i,
              title: "New",
              views: _.where(objectViews, {
                id: first_param + "-obj",
                create: true,
              }),
              url: currentUrl,
              type: "team",
              objectId: parseInt(third_param),
              params: customParams,
            };

          case "e":
            return {
              icon: "users",
              id: "obj-" + third_param + " " + i,
              title: "Edit " + fourth_param.split("%d%").join("-"),
              views: _.where(objectViews, {
                id: first_param + "-obj",
                edit: true,
              }),
              url: currentUrl,
              type: "team",
              objectId: parseInt(third_param),
              params: customParams,
            };

          default:
            return {
              icon: "users",
              id: "obj-" + second_param + " " + i,
              title: third_param.split("%d%").join("-"),
              views: _.where(objectViews, { id: first_param + "-obj" }),
              url: currentUrl,
              type: "team",
              objectId: parseInt(second_param),
              params: customParams,
            };
        }
      }

      function getCustomParams(
        first_param,
        second_param,
        third_param,
        fourth_param
      ) {
        var customParams = {};
        _.each(arguments, function (arg) {
          if (!_.isEmpty(arg) && typeof arg === "string") {
            _.each(arg.split("!"), function (param) {
              if (
                !_.isEmpty(param) &&
                typeof param === "string" &&
                param.split(":").length > 1
              ) {
                var parameter = param.split(":");
                customParams[parameter[0]] = parameter[1];
              }
            });
          }
        });

        return customParams;
      }

      function crawl_layers(i, onComplete) {
        var pageData = urlData[i.toString()];

        if (pageData === undefined) {
          loginToPortal(state, function (state) {
            if (typeof currentView.beforeLoad === "function") {
              currentView.beforeLoad(state, function (state) {
                onComplete({
                  layers: activeLayers,
                  activeLayer: currentLayer,
                  active: currentView,
                  state: state,
                });
              });
            } else {
              onComplete({
                layers: activeLayers,
                activeLayer: currentLayer,
                active: currentView,
                state: state,
              });
            }
          });

          return;
        }

        var type = pageData.split("-")[0];
        var pageName = "";
        var tool = "";
        var first_param = pageData.split("-")[1];
        var second_param = pageData.split("-")[2];
        var third_param = pageData.split("-")[3];
        var fourth_param = pageData.split("-")[4];

        currentUrl += "&" + i.toString() + "=" + encodeURIComponent(pageData);

        switch (type) {
          case "hqt": // headquarters tool
            tool = _.findWhere(hqTools, { id: first_param });
            state._toolId = second_param;
            pageName = tool.name;
            if (!_.isEmpty(second_param)) {
              pageName = second_param;
            }
            activeLayers.push({
              icon: "tool",
              id: "hqtools" + i,
              title: pageName,
              views: hqTools,
              url: currentUrl,
              tool: tool,
            });
            currentLayer = {
              icon: "tool",
              id: "hqtools" + i,
              title: pageName,
              views: hqTools,
              url: currentUrl,
              tool: tool,
            };
            currentView = _.findWhere(currentLayer.views, { id: first_param });
            break;

          case "tt": // team tool
            tool = _.findWhere(teamTools, { id: first_param });
            if (!_.isEmpty(third_param)) {
              pageName = third_param;
              state._toolId = parseInt(second_param);
            } else if (!_.isEmpty(second_param)) {
              pageName = second_param;
            } else if (_.findWhere(teamTools, { id: first_param })) {
              pageName = tool.name;
            }
            activeLayers.push({
              icon: "tool",
              id: "team-tools" + i,
              title: pageName,
              views: teamTools,
              url: currentUrl,
              tool: tool,
            });
            currentLayer = {
              icon: "tool",
              id: "team-tools" + i,
              title: pageName,
              views: teamTools,
              url: currentUrl,
              tool: tool,
            };
            currentView = _.findWhere(currentLayer.views, { id: first_param });
            break;

          case "jt": // job type tool
            tool = _.findWhere(jobTypeTools, { id: first_param });
            pageName = tool.name;
            activeLayers.push({
              icon: "tool",
              id: "job-type-tools" + i,
              title: pageName,
              views: jobTypeTools,
              url: currentUrl,
              tool: tool,
            });
            currentLayer = {
              icon: "tool",
              id: "job-type-tools" + i,
              title: pageName,
              views: jobTypeTools,
              url: currentUrl,
              tool: tool,
            };
            currentView = _.findWhere(currentLayer.views, { id: first_param });
            break;

          case "pt": // projects tool
            tool = _.findWhere(projectTools, { id: first_param });
            pageName = tool.name;
            if (!_.isEmpty(second_param)) {
              pageName = second_param;
            }
            activeLayers.push({
              icon: "tool",
              id: "project-tools" + i,
              title: pageName,
              views: projectTools,
              url: currentUrl,
              tool: tool,
            });
            currentLayer = {
              icon: "tool",
              id: "project-tools" + i,
              title: pageName,
              views: projectTools,
              url: currentUrl,
              tool: tool,
            };
            currentView = _.findWhere(currentLayer.views, { id: first_param });
            break;

          case "mst": // mystuff tool
            tool = _.findWhere(myStuff, { id: first_param });
            if (!_.isEmpty(third_param)) {
              pageName = third_param;
              state._toolId = parseInt(second_param);
            } else if (!_.isEmpty(second_param)) {
              pageName = second_param;
            } else if (_.findWhere(myStuff, { id: first_param })) {
              pageName = tool.name;
            }
            activeLayers.push({
              icon: "tool",
              id: "mystuff-tools" + i,
              title: pageName,
              views: myStuff,
              url: currentUrl,
              tool: tool,
            });
            currentLayer = {
              icon: "tool",
              id: "mystuff-tools" + i,
              title: pageName,
              views: myStuff,
              url: currentUrl,
              tool: tool,
            };
            currentView = _.findWhere(currentLayer.views, { id: first_param });
            break;

          case "o": // object single view
            activeLayers.push(
              getObjectView(
                first_param,
                second_param,
                third_param,
                fourth_param
              )
            );
            currentLayer = getObjectView(
              first_param,
              second_param,
              third_param,
              fourth_param
            );
            currentView = currentLayer.views[0];

            if (currentView) {
              currentView.params = currentLayer.params;

              state.id = currentLayer.objectId;
              if (currentLayer.type === "error-msg") {
                upstreamError = true;
              }

              switch (first_param) {
                case "project":
                  state.project = parseInt(second_param);
                  break;

                case "team":
                  state.team = parseInt(second_param);
                  break;
              }

              if (!_.isEmpty(currentView.view)) {
                currentView.dom = function (ui, state, onDraw) {
                  sb.notify({
                    type: "view-page",
                    data: {
                      onDraw: onDraw,
                      page: currentView.view,
                      state: state,
                      ui: ui,
                    },
                  });
                };
              }
            }

            break;

          case "e": // single entity object view
            activeLayers.push({
              icon: "page",
              id: "e-" + i,
              title: second_param,
              views: _.where(objectViews, { id: "entity-obj" }),
              url: currentUrl,
              type: "entity",
              objectId: parseInt(first_param),
            });
            currentLayer = {
              icon: "page",
              id: "e-" + i,
              title: second_param,
              views: _.where(objectViews, { id: "entity-obj" }),
              url: currentUrl,
              type: "entity",
              objectId: parseInt(first_param),
            };
            currentView = currentLayer.views[0];
            state.id = currentLayer.objectId;
            currentView.params = getCustomParams(
              first_param,
              second_param,
              third_param,
              fourth_param
            );
            break;

          default:
            tool = _.findWhere(customViews, { id: type });
            currentView = tool;
            currentView.params = getCustomParams(
              first_param,
              second_param,
              third_param,
              fourth_param
            );

            if (!currentView) {
              currentView = ErrorPage;
            } else {
              var title = currentView.name || currentView.title;
              if (currentView.params) {
                if (currentView.params.hasOwnProperty("name")) {
                  if (currentView.params.name) {
                    title = currentView.params.name;
                  }
                }
              }
              currentLayer = {
                icon: "tool",
                id: "custom-" + i,
                title: title,
                views: [currentView],
                url: currentUrl,
                tool: tool,
              };
              activeLayers.push({
                icon: "tool",
                id: "custom-" + i,
                title: title,
                views: [currentView],
                url: currentUrl,
                tool: tool,
              });
            }
            if (currentView.portal) {
              state.portal = true;
              state.getPortal = currentView.getPortal || false;
            }

            break;
        }

        i++;

        if (currentView === undefined || upstreamError) {
          currentView = ErrorPage;
        } else {
          state.params = currentView.params;
        }

        loginToPortal(state, function (state) {
          if (typeof currentView.beforeLoad === "function") {
            currentView.beforeLoad(state, function (state) {
              if (urlData.hasOwnProperty(i.toString())) {
                crawl_layers(i, onComplete);
              } else {
                onComplete({
                  layers: activeLayers,
                  activeLayer: currentLayer,
                  active: currentView,
                  state: state,
                });
              }
            });
          } else {
            if (urlData.hasOwnProperty(i.toString())) {
              crawl_layers(i, onComplete);
            } else {
              function getLayerObjectData(onComplete) {
                // Get data for each layer
                if (!_.isEmpty(activeLayers)) {
                  var layerIds = _.chain(activeLayers)
                    .pluck("objectId")
                    .compact()
                    .value();

                  if (!_.isEmpty(layerIds)) {
                    sb.data.db.obj.getById(
                      "",
                      layerIds,
                      function (layerData) {
                        onComplete(layerData);
                      },
                      1,
                      true
                    );
                  } else {
                    onComplete([]);
                  }
                } else {
                  onComplete([]);
                }
              }

              getLayerObjectData(function (layerData) {
                _.each(activeLayers, function (layer) {
                  if (layer.id === "headquarters") {
                    layer.pageObject = appConfig.headquarters;
                    layer.pageObjectType =
                      appConfig.headquarters.object_bp_type;
                  } else if (layer.id === "my-stuff") {
                    sb.data.db.obj.getWhere(
                      "groups",
                      {
                        user: +sb.data.cookie.get("uid"),
                        group_type: "MyStuff",
                      },
                      function (myStuffGroup) {
                        layer.pageObject = myStuffGroup[0];
                        layer.pageObjectType = "myStuff";
                      }
                    );
                  } else if (layer.objectId) {
                    var pageObject = _.findWhere(layerData, {
                      id: layer.objectId,
                    });
                    if (pageObject) {
                      layer.pageObject = pageObject;
                      layer.pageObjectType = pageObject.object_bp_type;
                    }
                  }
                });

                onComplete({
                  layers: activeLayers,
                  activeLayer: currentLayer,
                  active: currentView,
                  state: state,
                });
              });
            }
          }
        });
      }

      function loginToPortal(state, callback) {
        function getPortal(state, callback) {
          if (_.findWhere(Portals, { id: state.portal })) {
            callback(_.findWhere(Portals, { id: state.portal }));
          } else if (typeof state.getPortal === "function") {
            state.getPortal(state, callback);
          } else {
            callback(false);
          }
        }

        if (state.portal) {
          getPortal(state, function (portal) {
            if (
              portal.instance === appConfig.instance &&
              parseInt(sb.data.cookie.get("p_uid")) === portal.contact
            ) {
              callback(state);

              // Log user into other instance.
            } else {
              sb.data.db.controller("getIPAddress", {}, function (ip) {
                var tryCount = 0;
                function createPortalCookie(onComplete) {
                  function submitRequest(onComplete) {
                    var createCookie = {
                      // staffId: 		0
                      staffId: state.portal,
                      ip_address: ip,
                      fingerprint: window.navigator.userAgent.replace(
                        /\D+/g,
                        ""
                      ),
                      platform: window.navigator.platform,
                      type: "clientPortal",
                    };

                    sb.data.db.controller(
                      "createCookie&pagodaAPIKey=" + appConfig.instance,
                      createCookie,
                      function (cookie) {
                        onComplete(cookie, createCookie);
                      }
                    );
                  }

                  submitRequest(function (cookie, request) {
                    if (cookie && cookie.token) {
                      onComplete(cookie);

                      // Collect metrics for Foundation Group Client Portal Logins
                      if (cookie.instance === "foundation_group") {
                        appConfig.isFoundationGroupPortal = true;
                        (function (e, t, o, n, p, r, i) {
                          e.visitorGlobalObjectAlias = n;
                          e[e.visitorGlobalObjectAlias] =
                            e[e.visitorGlobalObjectAlias] ||
                            function () {
                              (e[e.visitorGlobalObjectAlias].q =
                                e[e.visitorGlobalObjectAlias].q || []).push(
                                arguments
                              );
                            };
                          e[e.visitorGlobalObjectAlias].l =
                            new Date().getTime();
                          r = t.createElement("script");
                          r.src = o;
                          r.async = true;
                          i = t.getElementsByTagName("script")[0];
                          i.parentNode.insertBefore(r, i);
                        })(
                          window,
                          document,
                          "https://diffuser-cdn.app-us1.com/diffuser/diffuser.js",
                          "vgo"
                        );
                        vgo("setAccount", "**********");
                        vgo("setTrackByDefault", true);
                        vgo("process");
                      }
                    } else if (tryCount < 5) {
                      tryCount++;
                      setTimeout(function () {
                        createPortalCookie(onComplete);
                      }, 500 * tryCount);
                    } else {
                      // Email Zach the latest request/response
                      // var args = {
                      //   to: "<EMAIL>",
                      //   from: appConfig.instance.emailFrom,
                      //   subject: "FG Client Login Issue Logs",
                      //   mergevars: {
                      //     TITLE: "FG Client Login Issue Logs",
                      //     BODY:
                      //       "COOKIE:<br />" +
                      //       JSON.stringify(cookie) +
                      //       "<br /><br />REQUEST:<br />" +
                      //       JSON.stringify(request) +
                      //       "<br /><br />CONFIG:<br />" +
                      //       JSON.stringify(appConfig),
                      //     BUTTON: "Log In",
                      //     BUTTON_LINK: "https://bento.voltz.software/app/",
                      //   },
                      //   emailtags: [""],
                      // };
                      // sb.comm.sendEmail(args, function (response) {
                      //   onComplete({});
                      // });
                    }
                  });
                }

                $("#loader").fadeIn();

                createPortalCookie(function (cookie) {
                  // Update app state
                  appConfig.rootInstance = {
                    instance: appConfig.instance,
                    is_portal: appConfig.is_portal,
                  };
                  appConfig.portal_company = cookie.company;
                  appConfig.portal_contact = portal.contact;
                  appConfig.instance = cookie.instance;

                  // Set current tokens
                  databaseConnection.setAppConfig(appConfig);
                  databaseConnection.setToken(cookie.token);

                  $("#loader").fadeOut();

                  get_instance_data({
                    toolRegistration: true,
                    onComplete: function () {
                      callback(state);

                      get_company_logo(appConfig, function (logo) {
                        companyLogo = logo[0];
                        viewLogo(LogoContainer, logo[0]);
                        LogoContainer.patch();
                      });
                    },
                  });
                });
              });
            }
          });
        } else {
          if (appConfig.instance !== RootInstance) {
            appConfig.instance = RootInstance.name;
            databaseConnection.setToken(RootInstance.token);
          }
          callback(state);
        }
      }

      var activeLayers = [];
      var currentUrl = window.location.href.split("#")[0] + "#";
      var upstreamError = false;
      var state = {};
      var rootPageParam = "";

      if (!window.location.href.split("#")[1]) {
        if ($(window).width() < 770) {
          rootPage = currentUrl + "home";
        } else {
          rootPage = currentUrl + "mystuff";
        }

        location.href = rootPage;
      }

      // get root page
      rootPage = window.location.href.split("#")[1].split("&")[0];
      if (rootPage.startsWith("p-")) {
        rootPageParam = rootPage.split("-")[1];
        rootPage = rootPage.split("-")[0];
      }

      switch (rootPage) {
        case "hq":
          currentUrl += "hq";
          activeLayers.push(
            _.findWhere(registerNavItems, { id: "headquarters" })
          );
          activeLayers[0].title = hq.name;
          activeLayers[0].url = currentUrl;
          state.id = hq.id;
          break;

        case "mystuff":
          currentUrl += "mystuff";
          activeLayers.push(_.findWhere(registerNavItems, { id: "my-stuff" }));
          activeLayers[0].title = "My Stuff";
          activeLayers[0].url = currentUrl;
          break;

        case "home":
          currentUrl += "home";
          activeLayers.push(_.findWhere(registerNavItems, { id: "home" }));
          activeLayers[0].title = "Home";
          activeLayers[0].url = currentUrl;
          break;

        case "settings":
          currentUrl += "settings";
          activeLayers.push(_.findWhere(registerNavItems, { id: "admin" }));
          activeLayers[0].title = "Settings";
          activeLayers[0].url = currentUrl;
          break;

        case "tickets":
          currentUrl += "tickets";
          activeLayers.push(_.findWhere(registerNavItems, { id: "tickets" }));
          activeLayers[0].title = "Tickets";
          activeLayers[0].url = currentUrl;
          break;

        case "logout":
          currentUrl += "logout";
          activeLayers.push({
            id: "logout",
            type: "custom",
            icon: "",
            default: true,
            dom: function (domObj, state, draw) {
              logout(domObj, state, draw);
            },
            views: [
              {
                id: "logout",
                type: "custom",
                icon: "",
                default: true,
                dom: function (domObj, state, draw) {
                  logout(domObj, state, draw);
                },
              },
            ],
          });
          activeLayers[0].title = "Logout";
          activeLayers[0].url = currentUrl;
          break;

        case "notifications":
          currentUrl += "notifications";
          activeLayers.push(
            _.findWhere(registerNavItems, { id: "notifications" })
          );
          activeLayers[0].title = "Notifications";
          activeLayers[0].url = currentUrl;
          break;

        case "p": // Portal
          currentUrl += "p-" + rootPageParam;
          activeLayers.push(
            _.findWhere(registerNavItems, {
              id: "portals",
            })
          );
          activeLayers[0].title = _.findWhere(Portals, {
            id: parseInt(rootPageParam),
          }).systemName;
          activeLayers[0].url = currentUrl;
          state.portal = parseInt(rootPageParam);
          break;

        default:
          try {
            currentUrl += rootPage;
            activeLayers.push({
              icon: "tool",
              id: "hqtools" + i,
              title: _.findWhere(navItems, { id: rootPage }).name,
              views: _.where(navItems, { id: rootPage }),
              url: currentUrl,
            });
          } catch (error) {
            console.log("e:", error);
            return false;
          }
          break;
      }

      var currentLayer = activeLayers[0];
      var currentView = _.findWhere(currentLayer.views, { default: true });

      // loop over urlDatums
      // split val into page params
      // store key and any other data needed to start up view, split by "-"
      var active = {};
      var i = 1;

      loginToPortal(state, function (state) {
        crawl_layers(i, onComplete);
      });
    }

    function load_view(currentView, state) {
      var viewName = "";

      function after_draw(resp) {
        if (resp && resp.dom) {
          resp.dom.patch();
          resp.after(resp.dom);
        } else if (resp &&  resp.hasOwnProperty('patch') ) {

            if ( typeof resp.patch === "function")
                resp.patch();

        }

        $(".ui.sticky").sticky({
          context: "#stickTo",
          offset: 12,
        });
      }

      function load_view_data(state, callback) {
        callback(state);
      }

      if (currentView.type === "object-view") {
        viewName = state.pageObject.name;
      } else {
        viewName = currentView.name || currentView.title;
      }

      document.title = viewName;

      $(mainView_UI.selector).fadeIn(80);

      appConfig.state = state;
      state.appSettings = appSettings;

      if (spaceNavigationColOpen === true) {
        if (currentView.size === "page") {
          $("#mainContainerArea").removeClass(
            viewWidth + " wide column transition"
          );
          $("#mainContainerArea").addClass(viewWidth + " wide column");
        } else {
          $("#mainContainerArea").removeClass(viewWidth + " wide column");
          $("#mainContainerArea").addClass(viewWidth + " wide column");
        }
      }

      // Sets > sets options
      if (
        state &&
        state.pageObject &&
        state.pageObject.object_bp_type === "entity_tool" &&
        !_.isEmpty(state.pageObject.settings)
      ) {
        state._config = {
          settings: state.pageObject.settings,
        };
      }

      // Add tool options to state
      if (currentView.type === "tool" && state.project && state.project.tools) {
        var toolConfig = _.findWhere(state.project.tools, {
          system_name: currentView.id,
        });
        if (toolConfig) {
          state._config = toolConfig;
        }
      } else if (
        currentView.type === "teamTool" &&
        state.team &&
        state.team.tools
      ) {
        var toolConfig = {};
        if (
          state._toolId &&
          _.findWhere(state.team.tools, {
            system_name: currentView.id,
            id: state._toolId,
          })
        ) {
          toolConfig = _.findWhere(state.team.tools, {
            system_name: currentView.id,
            id: state._toolId,
          });
        } else {
          toolConfig = _.findWhere(state.team.tools, {
            system_name: currentView.id,
          });
        }

        if (toolConfig) {
          state._config = toolConfig;
        }
      } else if (
        currentView.type === "hqTool" &&
        state.headquarters &&
        state.headquarters.tools
      ) {
        var toolConfig;
        _.each(state.headquarters.tools, function (toolObj) {
          if (toolObj) {
            if (toolObj.settings) {
              if (toolObj.settings.name) {
                if (toolObj.settings.name == state._toolId) {
                  toolConfig = toolObj;
                }
              }
            }
          }
        });

        if (toolConfig) {
          state._config = toolConfig;
        }
      }

      if (currentView.dom) {
        try {
          load_view_data(state, function (state) {
            currentView.dom(Main.Body, state, after_draw, "single");

            DrawPageMenu(state);

            DrawToolBarMenu(state, generate_toolsLimit());

            systemToolbar_refresh = function (newState) {
              DrawToolBarMenu(newState, generate_toolsLimit());
            };
          });
        } catch (error) {
          console.log("e:", error);

          ErrorPage.dom(Main.Body, state, after_draw);
        }
      } else if (currentView.mainViews) {
        try {
          load_view_data(state, function (state) {
            currentView.mainViews[0].dom(
              Main.Body,
              state,
              after_draw,
              "single"
            );

            DrawPageMenu(state);

            DrawToolBarMenu(state, generate_toolsLimit());
          });
        } catch (error) {
          console.log("e:", error);
          ErrorPage.dom(Main.Body, state, after_draw);
        }
      }
    }

    function refresh_page(doneCallback) {
      function get_state_objs(state, callback) {
        // Check what objects we need to get.
        var ids = [];
        if (
          typeof state.project === "number" &&
          !_.findWhere(Data.objects, { id: parseInt(state.project) })
        ) {
          ids.push(state.project);
        } else if (_.findWhere(Data.objects, { id: state.project })) {
          state.project = _.findWhere(Data.objects, { id: state.project });
        }
        if (
          typeof state.team === "number" &&
          !_.findWhere(Data.objects, { id: parseInt(state.team) })
        ) {
          ids.push(state.team);
        } else if (_.findWhere(Data.objects, { id: state.team })) {
          state.team = _.findWhere(Data.objects, { id: state.team });
        }

        if (!_.isEmpty(ids)) {
          sb.data.db.obj.getById(
            "groups",
            ids,
            function (objs) {
              // cache project/team values
              if (typeof state.project === "number") {
                state.project = _.findWhere(objs, { id: state.project });
                Data.objects.push(
                  _.findWhere(objs, { id: parseInt(state.project) })
                );
              }
              if (typeof state.team === "number") {
                state.team = _.findWhere(objs, { id: state.team });
                Data.objects.push(
                  _.findWhere(objs, { id: parseInt(state.team) })
                );
              }

              callback(state);
            },
            2
          );
        } else {
          callback(state);
        }
      }

      sb.data.db.trackXhr();

      get_page_state(
        urlData,
        function () {
          if (doneCallback) {
            doneCallback(true);
          }

          if (state.toErrorPage) {
            currentView = ErrorPage;
          }

          state.headquarters = hq;
          if (App_State.project) {
            state.project = App_State.project;
          }
          if (App_State.team) {
            state.team = App_State.team;
          }

          get_state_objs(state, function (state) {
            appConfig.state = state;
            Main.refreshHeader(state, currentView);
            load_view(currentView, state);

            // Feature block, for dev, Foundation Group, Foundation Group's client
            // instances, and the company's instances
            // if (
            // 	appConfig.instance === 'rickyvoltz'
            // 	|| appConfig.instance === 'voltzsoftware'
            // 	|| appConfig.instance === 'foundation_group'
            // 	|| appConfig.is_portal === true
            // ) {
            // 	buildRightTray(rightTrayContainer, Layers);
            // }
          });

          App_UI = ui;
        },
        state,
        currentView
      );
    }

    function build_mainSystemBar(ui, state) {
      var userProfile_image = {
        img: "",
      };

      var notificationViewObj = _.findWhere(registerNavItems, {
        id: "notifications",
      });
      var timeTrackingViewObj = _.findWhere(registerNavItems, {
        id: "timeTracking",
      });
      var href = "";

      if (
        currentUser.profile_image.loc != "//" &&
        currentUser.profile_image !== null &&
        currentUser.profile_image !== undefined
      ) {
        userProfile_image.img =
          '<img class="ui avatar image" src="' +
          sb.data.files.getURL(currentUser.profile_image) +
          '">';
      } else {
        userProfile_image.img = '<i class="large user circle icon"></i>';
      }

      if (_.contains(hq.managers, currentUser.id)) {
        href = window.location.href.split("#")[0] + "#hq";
      } else {
        href = window.location.href.split("#")[0] + "#mycrm";
      }

      ui.makeNode("modals", "div", {});

      var middleNav = ui.makeNode("middleNav", "div", {
        css: "ui vertical icon fluid borderless secondary menu middleNavigation",
        style:
          "margin-left:0 !important; margin-bottom:0 !important; position:absolute; left:0; bottom:45px; width:65px !important;",
      });

      var bottomNav = ui.makeNode("bottomNav", "div", {
        css: "ui vertical icon fluid borderless secondary inverted menu bottomNavigation",
        style:
          "margin-left:0 !important; margin-top:0 !important; position:absolute; left:0; bottom:0px; width:65px !important;",
      });

      if (!appConfig.is_portal) {
        middleNav.makeNode("itemone", "div", {
          css: "item text-center centered",
          tag: "a",
          listener: {
            type: "popup",
            hoverable: true,
            delay: {
              show: 500,
              hide: 0,
            },
          },
          tooltip: {
            title: "Time Sheet",
            text: "",
            position: "right center",
          },
        });
      }

      middleNav.makeNode("itemtwo", "div", {
        tag: "a",
        css: "item text-center centered",
        listener: {
          type: "popup",
          hoverable: true,
          delay: {
            show: 500,
            hide: 0,
          },
        },
        tooltip: {
          title: "Notifications",
          text: "",
          position: "right center",
        },
      });
      // !WALK step-one
      middleNav.makeNode("profile", "div", {
        css: "item text-center centered",
        style: "",
        tag: "a",
        text: userProfile_image.img,
        // 				text: userProfile_image.img + ' <span>' + currentUser.fname + ' ' + currentUser.lname + '</span>',
        href: window.location.href.split("#")[0] + "#myaccount",
        walkthrough: {
          name: "welcome",
          step: "01-intro",
        },
        listener: {
          type: "popup",
          hoverable: true,
          delay: {
            show: 500,
            hide: 0,
          },
        },
        tooltip: {
          title: "Profile",
          text: "",
          position: "right center",
        },
      });

      bottomNav.makeNode("logout", "div", {
        tag: "a",
        css: "item text-center centered logoutBtn",
        style:
          "padding-top:15px !important; padding-bottom:15px !important; border-radius:0 !important;",
        text: '<i style="" class="sign out alt icon"></i>',
        href: window.location.href.split("#")[0] + "#logout",
        listener: {
          type: "popup",
          hoverable: true,
          delay: {
            show: 500,
            hide: 0,
          },
        },
        tooltip: {
          title: "Logout",
          text: "",
          position: "right center",
        },
      });

      if (timeTrackingViewObj && !appConfig.is_portal) {
        timeTrackingViewObj.menuItemView(middleNav.itemone, { ui: middleNav });
      }
      if (notificationViewObj) {
        notificationViewObj.menuItemView(middleNav.itemtwo);
      }
    }

    function generate_toolsLimit() {
      if (
        $(".mainContainerArea").width() >= 200 &&
        $(".mainContainerArea").width() <= 601
      ) {
        return 1;
      } else if (
        $(".mainContainerArea").width() >= 601 &&
        $(".mainContainerArea").width() <= 900
      ) {
        return 2;
      } else if (
        $(".mainContainerArea").width() >= 901 &&
        $(".mainContainerArea").width() <= 1000
      ) {
        return 3;
      } else if (
        $(".mainContainerArea").width() >= 1001 &&
        $(".mainContainerArea").width() <= 1100
      ) {
        return 4;
      } else if (
        $(".mainContainerArea").width() >= 1101 &&
        $(".mainContainerArea").width() <= 1200
      ) {
        return 5;
      } else if (
        $(".mainContainerArea").width() >= 1201 &&
        $(".mainContainerArea").width() <= 1300
      ) {
        return 5;
      } else if (
        $(".mainContainerArea").width() >= 1301 &&
        $(".mainContainerArea").width() <= 1400
      ) {
        return 6;
      } else if (
        $(".mainContainerArea").width() >= 1401 &&
        $(".mainContainerArea").width() <= 1500
      ) {
        return 7;
      } else if (
        $(".mainContainerArea").width() >= 1501 &&
        $(".mainContainerArea").width() <= 1600
      ) {
        return 8;
      } else if (
        $(".mainContainerArea").width() >= 1601 &&
        $(".mainContainerArea").width() <= 1700
      ) {
        return 9;
      } else if (
        $(".mainContainerArea").width() >= 1701 &&
        $(".mainContainerArea").width() <= 1800
      ) {
        return 10;
      } else if (
        $(".mainContainerArea").width() >= 1801 &&
        $(".mainContainerArea").width() <= 1900
      ) {
        return 11;
      } else if (
        $(".mainContainerArea").width() >= 1901 &&
        $(".mainContainerArea").width() <= 2000
      ) {
        return 12;
      } else if (
        $(".mainContainerArea").width() >= 2001 &&
        $(".mainContainerArea").width() <= 2100
      ) {
        return 13;
      } else {
        return 50;
      }
    }

    function build_systemToolbar(ui, hideIcons, edit) {
      //var edit = true;

      var layer = CurrentLayer;
      var toolBarState = undefined;

      if (Layers.slice(-1)[0].title == "Add Tools") {
        edit = true;
      }

      function draw_toolBar(ui, config, draw, toolLimit, hideIcons, edit) {
        function build_toolsSelection(ui, setup) {
          var sortedTools = _.chain(setup.options)
            .filter(function (o) {
              return o.id !== "pagesTool";
            })
            .sortBy(function (o) {
              return o.name;
            })
            .value();
          var mainDom = ui;

          function display_tools(ui, toolsList) {
            ui.makeNode("cards", "div", {
              css: "ui centered cards",
            });

            var i = 0;
            _.each(toolsList, function (tool) {
              if (true) {
                ui.cards.makeNode("tool-" + i, "div", {
                  css: "card",
                });

                ui.cards["tool-" + i].makeNode("content", "div", {
                  css: "content",
                });
                ui.cards["tool-" + i].content.makeNode("header", "div", {
                  css: "header",
                  text:
                    '<i class="' +
                    tool.icon.color +
                    " " +
                    tool.icon.type +
                    ' icon"></i> ' +
                    tool.name,
                });
                ui.cards["tool-" + i].content.makeNode("description", "div", {
                  css: "description",
                  text: tool.tip,
                });
                ui.cards["tool-" + i]
                  .makeNode("btn", "div", {
                    css: "ui mini bottom attached button",
                    text: '<i class="add icon"></i> add tool',
                  })
                  .notify(
                    "click",
                    {
                      type: "run-method",
                      data: {
                        run: function (data) {
                          setup.add(tool.id, function (config) {
                            config.refresh = configCache.refresh;

                            toolBarMenu(state, 100, false, true);

                            mainDom.empty();
                            build_toolsSelection(mainDom, config.add);
                            mainDom.patch();
                          });
                        },
                      },
                    },
                    sb.moduleId
                  );
                i++;
              }
            });
          }

          ui.makeNode("wrapper", "div", {});

          ui.wrapper.makeNode("head", "div", {
            css: "ui basic segment",
          });
          //ui.wrapper.makeNode('lb_1', 'lineBreak', {spaces: 1});
          ui.wrapper.makeNode("body", "div", {});
          ui.wrapper.makeNode("lb_2", "lineBreak", { spaces: 2 });

          ui.wrapper.head.makeNode("search", "div", {
            css: "ui search text-center",
            id: "addPageTypeMenu",
          });

          ui.wrapper.head.search.makeNode("iconInput", "div", {
            css: "ui icon input",
            style: "width: 50%; margin: 0 auto;",
          });
          ui.wrapper.head.search.iconInput.makeNode("input", "div", {
            tag: "input",
            id: "addPageTypeMenuSearch",
            css: "prompt",
            placeholder: "Search tools...",
          });

          $(window).on("input", function (e) {
            if (e.target.id === "addPageTypeMenuSearch") {
              var matches = setup.options.filter(function (o) {
                return o.name
                  .toLowerCase()
                  .includes(e.target.value.toLowerCase());
              });

              ui.wrapper.body.empty();

              if (_.isEmpty(matches)) {
                ui.wrapper.body.makeNode("noRes", "div", {
                  text: "No results found",
                  css: "ui header text-center",
                  tag: "h3",
                });
              } else {
                display_tools(ui.wrapper.body, matches);
              }

              ui.wrapper.body.patch();
            }
          });

          ui.wrapper.head.search.iconInput.makeNode("icon", "div", {
            css: "search icon",
            tag: "i",
          });

          display_tools(ui.wrapper.body, sortedTools);
        }

        if (Layers.slice(-1)[0].title == "Add Tools") {
          edit = true;
        }

        if (!hideIcons) {
          hideIcons = false;
        }

        toolBarState = {};

        toolBarState.ui = ui;
        toolBarState.config = config;
        toolBarState.draw = draw;
        toolBarState.toolLimit = toolLimit;

        var menuItems = config.links;
        var titleCss = "";
        var remainingItems = [];
        var toolsLimit = 100;
        var instanceCheck = false;

        if (
          appConfig.state.hasOwnProperty("pageObject") &&
          typeof appConfig.state.pageObject !== "undefined"
        ) {
          if (appConfig.instance == appConfig.state.pageObject.instance) {
            instanceCheck = true;
          }
        }

        if (layer.id === CurrentLayer.id) {
          titleCss = "active ";
        }

        if (toolLimit !== undefined) {
          toolsLimit = toolLimit;
        }

        toolsLimit = 100;

        var spaceName = "";

        if (layer.title.length >= 15) {
          spaceName = layer.title.substring(0, 15) + "..";
        } else {
          spaceName = layer.title;
        }

        ui.empty();

        var obj = {};

        obj = config.group;

        // Set the icon and space link
        if (obj) {
          switch (obj.group_type) {
            case "Team":
              spaceIcon = '<i class="users icon"></i>';
              spaceLink = sb.data.url.createPageURL("object", {
                id: obj.id,
                name: obj.name,
                type: obj.group_type.toLowerCase(),
              });
              break;

            case "Project":
              spaceIcon = '<i class="project diagram icon"></i>';
              spaceLink = sb.data.url.createPageURL("object", {
                id: obj.id,
                name: obj.name,
                type: obj.group_type.toLowerCase(),
              });
              break;

            case "Headquarters":
              spaceIcon = '<i class="building icon"></i>';
              spaceLink = window.location.href.split("#")[0] + "#hq";
              break;

            case "MyStuff":
              spaceIcon = '<i class="home icon"></i>';
              spaceLink = window.location.href.split("#")[0] + "#mystuff";
              break;

            default:
              spaceLink = sb.data.url.createPageURL("object", {
                id: obj.id,
                name: obj.name,
                type: obj.object_bp_type.toLowerCase(),
              });

              if (obj.object_bp_type === "entity_type") {
                spaceIcon = '<i class="' + obj.icon + ' icon"></i>';
                spaceLink = layer.url;
              }

              break;
          }
        }

        if (appConfig.state.hasOwnProperty("profileSetup")) {
          if (appConfig.state.profileSetup == "teams") {
            obj = appConfig.user.myStuff;
          }
        }

        var menuCSS = "ui secondary vertical fluid menu";
        var centerList = "";
        if (hideIcons === true) {
          centerList = "text-align:center;";
          menuCSS = "ui icon vertical secondary fluid menu";
        }

        ui.makeNode("list", "div", { css: menuCSS, style: centerList });

        menuItems = _.filter(menuItems, function (o) {
          return o.id !== "pagesTool";
        });

        var filteredMenuItems = _.chain(menuItems)
          .filter(function (item) {
            if (_.isEmpty(state.searchString)) {
              return true;
            }

            return item.title
              .toUpperCase()
              .includes(state.searchString.toUpperCase());
          })
          .value();
        if (!config.maintainOrder) {
          filteredMenuItems = _.sortBy(filteredMenuItems, "title");
        }

        _.each(filteredMenuItems, function (item, i) {
          if (i <= toolsLimit) {
            if (typeof item.view === "function") {
              // 							item.view(ui.mb);
            } else {
              var activeClass = "";
              var linkTag = "a";
              if (currentView.id === item.id) {
                activeClass = "active";
              }
              if (state._toolId === item.title) {
                activeClass = "active";
              }
              if (
                currentView.allowMulti &&
                item.toolId &&
                state._toolId &&
                item.title != state._toolId
              ) {
                activeClass = "";
              }

              if (edit) {
                linkTag = false;
              }

              if (hideIcons && !edit) {
                ui.list.makeNode("i-" + i, "div", {
                  tag: linkTag,
                  text: '<i class="' + item.icon + ' icon"></i>',
                  css: "" + activeClass + " item",
                  href: item.link,
                  listener: {
                    type: "popup",
                    hoverable: true,
                  },
                  tooltip: {
                    title: item.title,
                    text: "",
                    position: "right center",
                  },
                });
              } else {
                if (edit && !hideIcons) {
                  var itemText = item.title;
                } else {
                  var itemText =
                    '<i class="' + item.icon + ' left icon"></i> ' + item.title;
                }

                ui.list.makeNode("i-" + i, "div", {
                  tag: linkTag,
                  text: itemText,
                  css: "ui dropdown " + activeClass + " item toolMenuSubItem",
                  href: item.link,
                });
              }

              if (edit) {
                if (typeof item.remove === "function") {
                  ui.list["i-" + i]
                    .makeNode("rm", "div", {
                      style: "float:right",
                      text: '<i class="red times icon"></i>',
                    })
                    .listeners.push(
                      function (selector) {
                        $(selector).click(function (e) {
                          e.preventDefault();
                          item.remove(function (x) {
                            config.add = x.add;

                            toolBarMenu(state, toolsLimit, false, true);

                            if (onAddPagesArea) {
                              Main.empty();
                              build_toolsSelection(Main, config.add);
                              Main.patch();
                            }
                          });
                        });
                      }.bind(item)
                    );
                }
              }
            }
          } else {
            remainingItems.push(item);
          }
        });

        if (!_.isEmpty(remainingItems)) {
          ui.makeNode("more", "div", {
            css: "item show-pointer",
            text: remainingItems.length + " more...",
          });

          // Popover
          ui.makeNode("morePopover", "popover", {
            parent: ui.more,
            trigger: "click",
            animation: "pop",
            placement: "right",
            autoHide: false,
            style: "padding:0px;border:0px;border-radius:4px;",
          });

          ui.morePopover.makeNode("menu", "div", {
            css: "ui vertical menu",
            style: "background-color: #4B4D57 !important;",
          });

          _.each(remainingItems, function (item, i) {
            ui.morePopover.menu
              .makeNode("i-" + i, "div", {
                tag: "a",
                text:
                  '<i class="' + item.icon + ' left icon"></i>' + item.title,
                css: "ui simple dropdown item toolBarSubMenuItem",
                href: item.link,
              })
              .notify(
                "click",
                {
                  type: "run-method",
                  data: {
                    run: function (data) {
                      onAddPagesArea = false;
                    },
                  },
                },
                sb.moduleId
              );

            ui.morePopover.makeNode("pop-" + i, "popover", {
              parent: ui.morePopover.menu["i-" + i],
              trigger: "hover",
              animation: "pop",
              placement: "right",
              style: "padding:0px;border:0px;border-radius:4px;",
            });

            ui.morePopover["pop-" + i].makeNode("menu", "div", {
              css: "ui vertical menu",
            });

            if (typeof item.remove === "function") {
              ui.morePopover["pop-" + i].menu
                .makeNode("rm", "div", {
                  css: "item",
                  text: '<i class="red times icon"></i> Remove ' + item.title,
                  style: "color: black;",
                  tag: "a",
                })
                .listeners.push(
                  function (selector) {
                    $(selector).click(function (e) {
                      e.preventDefault();
                      item.remove(function (x) {
                        config.add = x.add;

                        toolBarMenu(state, toolsLimit);

                        if (onAddPagesArea) {
                          Main.empty();
                          build_toolsSelection(Main, config.add);
                          Main.patch();
                        }
                      });
                    });
                  }.bind(item)
                );
            }
          });
        }

        var addIconText = " add tools";
        var editIconText = " edit tools";
        var clearIconText = " clear tools";

        if (hideIcons) {
          addIconText = "";
          editIconText = "";
          clearIconText = "";
        }

        var lastURLSegment = sb.dom.lastURLSegment(window.location.href);

        if (!_.isEmpty(obj)) {
          if (obj.hasOwnProperty("object_bp_type")) {
            if (obj.object_bp_type === "groups") {
              if (appConfig.state.pageObject) {
                isAdmin = sb.permissions.isGroupManager(
                  +sb.data.cookie.userId,
                  appConfig.state.pageObject
                );
              }
            }
          }
        }

        if (
          (isAdmin || (layer.id == "my-stuff" && !appConfig.is_portal)) &&
          instanceCheck
        ) {
          if (edit && !hideIcons) {
            var addPagesLink = sb.data.url.createPageURL("hqTool", {
              tool: "pagesTool",
            });

            if (lastURLSegment != "pagesTool") {
              ui.list.makeNode("add", "div", {
                tag: "a",
                href: addPagesLink,
                text: '<i class="plus yellow icon"></i>' + addIconText,
                css: "item show-pointer editToolsItem",
                style: "font-weight:bold; border-radius:0 !important;",
              });
            }

            ui.list
              .makeNode("resetToolsItem", "div", {
                css: "item show-pointer",
                style: "font-weight:bold",
                text: '<i class="trash red icon"></i>' + clearIconText,
                listener: {
                  type: "popup",
                  hoverable: true,
                },
              })
              .notify(
                "click",
                {
                  type: "run-method",
                  data: {
                    run: function (data) {
                      sb.dom.alerts.ask(
                        {
                          title: "Are you sure?",
                          text: "Are you sure you want to remove all tools in this area? This can not be undone.",
                        },
                        function (r) {
                          if (r === true) {
                            swal.disableButtons();

                            if (obj === undefined) {
                              sb.data.db.obj.erase(
                                "entity_tool",
                                _.pluck(config.links, "id"),
                                function (r) {
                                  config.links = [];
                                  swal.close();
                                  toolBarMenu(state, toolsLimit);
                                }
                              );

                              sb.notify({
                                type: "field-updated",
                                data: {
                                  obj: obj,
                                  property: "tools",
                                },
                              });
                            } else {
                              obj.tools = [];

                              sb.data.db.obj.update(
                                obj.object_bp_type,
                                {
                                  id: obj.id,
                                  tools: [],
                                },
                                function (r) {
                                  swal.close();
                                  toolBarMenu(state, toolsLimit);
                                }
                              );

                              sb.notify({
                                type: "field-updated",
                                data: {
                                  obj: obj,
                                  property: "tools",
                                },
                              });
                            }
                          } else {
                            swal.close();
                          }
                        }
                      );
                    },
                  },
                },
                sb.moduleId
              );

            ui.list
              .makeNode("doneEditingItem", "div", {
                css: "item show-pointer",
                style: "font-weight:bold",
                text: '<i class="check icon green"></i> done editing',
              })
              .notify(
                "click",
                {
                  type: "run-method",
                  data: {
                    run: function (data) {
                      if (lastURLSegment === "pagesTool") {
                        sb.notify({
                          type: "app-navigate-to",
                          data: {
                            type: "UP",
                          },
                        });
                      } else {
                        toolBarMenu(state, toolLimit, hideIcons, false);
                      }
                    },
                  },
                },
                sb.moduleId
              );
          } else {
            if (!hideIcons) {
              ui.list
                .makeNode("editToolsItem", "div", {
                  css: "grey item show-pointer editToolsItem",
                  style: "border-radius:0 !important;",
                  text: '<i class="pencil grey icon"></i>' + editIconText,
                })
                .notify(
                  "click",
                  {
                    type: "run-method",
                    data: {
                      run: function (data) {
                        toolBarMenu(state, toolLimit, hideIcons, true);
                      },
                    },
                  },
                  sb.moduleId
                );
            }
          }
        }

        DrawPageSelection = function () {
          Main.empty();

          build_toolsSelection(Main, config.add);

          Main.patch();
        };

        if (lastURLSegment === "pagesTool") {
          onAddPagesArea = true;

          DrawPageSelection();
        }

        ui.patch();

        $(".ui.dropdown").dropdown({
          on: "hover",
          onShow: function () {
            var event = this;

            if ($(event).hasClass("toolMenuSubItem")) {
              setTimeout(function () {
                var el = $($(event).children()[0]);

                var left, top;

                left = el.offset().left;
                top = el.offset().top;

                $(el).css({
                  position: "fixed",
                  left: left + "px",
                  top: top + "px",
                });
              }, 100);
            }
          },
        });
      }

      function toolBarMenu(state, toolLimit, hideIcons, edit) {
        if (
          ((currentView.menu === undefined &&
            (currentView.type === "object-view" ||
              currentView.type === "custom" ||
              currentView.id === "myaccount" ||
              currentView.id === "mytimelog")) ||
            appConfig.is_portal ||
            sb.dom.lastURLSegment(window.location.href) === "myteams") &&
          !appConfig.isFoundationGroupPortal
        ) {
          $("#spaceNavigationCol").addClass("space-navigation-col-hidden");
          $("#spaceViewCol").css({ "border-left": "none" });
          $(".mainViewContainer").css("clear", "both");
          $(ui.selector).addClass("animated fadeOut");
          $(ui.selector).addClass("visibility-none");

          if (spaceNavigationColOpen === true) {
            autoClosed = true;

            sb.notify({
              type: "close-toolBar",
              data: {
                state: state,
              },
            });
          }

          return;
        } else {
          $("#spaceNavigationCol").removeClass("space-navigation-col-hidden");
          $("#spaceViewCol").css({ "border-left": "1px solid #ebebeb;" });
          $(ui.selector).removeClass("animated fadeOut");
          $(ui.selector).removeClass("visibility-none");

          if (autoClosed === true) {
            autoClosed = false;

            sb.notify({
              type: "open-toolBar",
              data: {
                state: state,
              },
            });
          }
        }

        if (!hideIcons) {
          if (spaceNavigationColOpen === true) {
            hideIcons = false;
          } else {
            hideIcons = true;
          }
        }

        if (spaceNavigationColOpen === true) {
          hideIcons = false;
        }

        if (typeof currentView.menu === "function") {
          currentView.menu(
            state,
            function (menu) {
              configCache = menu;

              draw_toolBar(
                ui,
                menu,
                function () {},
                //, CurrentLayer
                toolLimit,
                hideIcons,
                edit
              );
            },
            layer
          );
        } else {
          // get first ancestor with a menu
          var menuFunc = false;
          _.each(Layers.slice(0).reverse(), function (menuLayer) {
            if (
              menuFunc === false &&
              !_.isEmpty(menuLayer.views) &&
              menuLayer.views[0] &&
              typeof menuLayer.views[0].menu === "function"
            ) {
              menuFunc = menuLayer.views[0].menu;
              layer = menuLayer;
            }
          });

          if (menuFunc) {
            menuFunc(
              state,
              function (menu) {
                configCache = menu;

                draw_toolBar(
                  ui,
                  menu,
                  function () {},
                  //, CurrentLayer
                  toolLimit,
                  hideIcons,
                  edit
                );
              },
              layer
            );
          }
        }

        // Move tables based on screen size
        sb.notify({
          type: "move-elements-based-on-screen-size",
          data: {
            window: window,
          },
        });
      }

      function openAddPageMenu(selector, setup) {
        function closeMenu() {
          dropdown.search("destroy");
          $("#" + popSelector).remove();
        }

        var pos = $(selector).offset();
        var scrollOffset = $(".toolBar").scrollTop();
        var popSelector = "addPageTypeMenu";

        $("body").append(
          '<div id="' +
            popSelector +
            '" class="ui search dropdown secondary segment" style="position:absolute;left:50%;top:80px;z-index:10000;">' +
            '<div class="ui transparent left icon input">' +
            '<i class="ui file icon"></i>' +
            '<input id="addPageTypeMenuSearch" class="prompt" type="text" placeholder="Add a page..">' +
            "</div>" +
            '<div class="results" style="max-height:400px;overflow-y:auto;width:600px;"></div>' +
            "</div>"
        );

        var content = [];
        var pageOptions = _.chain(setup.options)
          .sortBy("name")
          .map(function (option) {
            return {
              title:
                '<i class="ui ' +
                option.icon.color +
                " " +
                option.icon.type +
                ' icon"></i> ' +
                option.name,
              description: option.tip,
              value: option.id,
            };
          })
          .value();

        var dropdown = $("#" + popSelector).search({
          selectFirstResult: true,
          searchOnFocus: true,
          minCharacters: 0,
          maxResults: pageOptions.length + 1,
          onSelect: function (selection) {
            $("#popSelector input").loading();

            if (!_.isEmpty(selection)) {
              setup.add(selection.value, function (config) {
                config.refresh = configCache.refresh;

                closeMenu();
                toolBarMenu(state);
                ui.patch();
              });
            }
          },
          source: pageOptions,
          searchFields: ["title"],
          fullTextSearch: true,
        });

        dropdown.search("show");
        dropdown.search("set active");

        $("#addPageTypeMenuSearch").focus();
        $("#addPageTypeMenuSearch").on("blur", function (e) {
          setTimeout(closeMenu, 100);
        });
        return;
      }

      DrawToolBarMenu = toolBarMenu;
    }

    if (_.contains(hq.managers, parseInt(sb.data.cookie.userId))) {
      isAdmin = true;
    } else {
      if (currentUser.type) {
        if (currentUser.type === "admin" || currentUser.type === "Admin") {
          isAdmin = true;
        }
      }
    }

    compile_layers(appConfig, function (LayerData) {
      if (LayerData === false) {
        if ($(window).width() < 770) {
          window.location.href = window.location.href.split("#")[0] + "#home";
        } else {
          if (hq.managers.indexOf(+sb.data.cookie.get("uid")) > 1) {
            window.location.href =
              window.location.href.split("#")[0] + "#mystuff";

            return;
          } else {
            window.location.href = window.location.href.split("#")[0] + "#home";

            return;
          }
        }
      }

      if (
        hq.managers.indexOf(+sb.data.cookie.get("uid")) == -1 &&
        window.location.href.split("#")[1] == "hq"
      ) {
        if ($(window).width() > 768) {
          window.location.href =
            window.location.href.split("#")[0] + "#mystuff";

          return;
        } else {
          window.location.href = window.location.href.split("#")[0] + "#home";

          return;
        }
      }

      EditModal;
      Layers = LayerData.layers;
      currentView = LayerData.active;
      CurrentLayer = LayerData.activeLayer;
      state = LayerData.state;
      Header = {};
      menuWidth = "three";
      viewWidth = "twelve";

      function breadcrumb_ui(ui) {
        var layers = Layers;
        var currentLayer = CurrentLayer;
        var hasDrawnEllipsis = false;

        function drawBreadcrumb(layer) {
          var charLimit = 30;
          var title =
            layer.title.length > charLimit
              ? layer.title.slice(0, charLimit) + "..."
              : layer.title;
          var showTooltip = layer.title.length > charLimit ? true : false;
          var color = "";

          if (currentLayer.id === layer.id) {
            color = "active";
          }

          title = !_.isEmpty(title) ? title : "Untitled";

          var options = {
            css: color + " section link",
            text: title,
            tag: "a",
            href: layer.url,
          };

          if (showTooltip) {
            options.tooltip = layer.title;
            options.tooltipPos = "bottom left";
          }

          ui.makeNode("layer-" + layer.id, "div", options);

          if (color != "active") {
            ui.makeNode("sep-" + layer.id, "div", {
              css: "divider",
              text: ' <i class="ui chevron right icon" style="margin:0 !important;"></i> ',
            });
          }
        }

        // Get breadcrumbs for ellipsis menu
        var ellipsisLayers = _.filter(layers, function (layer, i) {
          if (layers.length > 4) {
            if (i > 0 && i < layers.length - 2) {
              return layer;
            }
          }
        });

        _.each(layers, function (layer, i) {
          if (!layer.title && (layer.tool || {}).id == "contactDashboard")
            layer.title = layer.tool.title;

          if (layers.length > 4) {
            if (i > 0 && i < layers.length - 2) {
              if (!hasDrawnEllipsis) {
                var ellipsis = ui.makeNode("ellipsis", "div", {
                  css: "ui simple dropdown section link",
                  style: "padding-top:0 !important;",
                  text: " ••• ",
                });
                ellipsisMenu = ellipsis.makeNode("menu", "div", {
                  css: "menu",
                });
                _.each(ellipsisLayers, function (layer) {
                  ellipsisMenu.makeNode("item-" + layer.id, "div", {
                    css: "item",
                    text:
                      '<i class="ui chevron right light-grey icon" style="margin:0px 7px 0px 0px !important; font-size:13px !important;"></i>' +
                      layer.title,
                    tag: "a",
                    href: layer.url,
                  });
                });
                ui.makeNode("sep-" + layer.id, "div", {
                  css: "divider",
                  text: ' <i class="ui chevron right icon" style="margin:0 !important;"></i> ',
                });
                hasDrawnEllipsis = true;
              }
            } else {
              drawBreadcrumb(layer);
            }
          } else {
            drawBreadcrumb(layer);
          }
        });
      }

      // Menu
      columnViewClass = "";

      var topGrid = ui.makeNode("topGrid", "div", {
        css: "ui stackable grid",
        style:
          "position:fixed; background-color:#ffffff; margin:0; width:100%; z-index:999;",
      });

      var topNav = ui.topGrid.makeNode("topNav", "div", {
        css: "sixteen wide column",
        style:
          "height:55px; padding-top:10px; border-bottom:1px solid #ebebeb;",
        id: "topNav",
      });

      var topNavGrid = topNav.makeNode("topNavGrid", "div", {
        css: "ui stackable grid",
      });

      var logoContainer = topNavGrid.makeNode("logoContainer", "div", {
        css: "two wide column",
        style:
          "margin:0; padding-top:0; padding-bottom:0; padding-left:8px !important; padding-right:8px !important; width:100px !important; text-align:center;",
      });
      var logoHelper = logoContainer.makeNode("logoHelper", "div", {
        style: "display:inline-block; height:100%; vertical-align:middle;",
      });
      var logo = logoContainer.makeNode("logo", "div", {
        style: "display:inline-block;",
      });
      LogoContainer = logo;
      viewLogo(LogoContainer, companyLogo);

      var breadcrumbsContainer = topNavGrid.makeNode(
        "breadcrumbsContainer",
        "div",
        {
          style:
            "padding-top: 0; padding-bottom: 0; display: flex; flex-direction: column; flex: 1 1 auto;",
        }
      );

      var breadcrumbs = breadcrumbsContainer.makeNode("breadcrumbs", "div", {
        css: "ui breadcrumb",
      });

      var activeLayers = Layers;
      appConfig.breadcrumbs = activeLayers;

      // Draw breadcrumbs
      breadcrumb_ui(breadcrumbs);

      var bottomGrid = ui.makeNode("bottomGrid", "div", {
        id: "bottomGrid",
        css: "ui stackable grid",
        style: "margin:0; height:100%; overflow:hidden;",
      });

      var leftNav = bottomGrid.makeNode("col1", "div", {
        css: "one wide column",
        id: "leftNav",
      });
      buildLeftNav(leftNav, state);

      var spaceNavigationColCSS = "space-navigation-col-open";
      if (spaceNavigationColOpen === false) {
        spaceNavigationColCSS = "space-navigation-col-closed";
      }
      var spaceNavigationCol = bottomGrid.makeNode("col2", "div", {
        css: spaceNavigationColCSS,
        id: "spaceNavigationCol",
      });

      var spaceViewCol = bottomGrid.makeNode("col3", "div", {
        id: "spaceViewCol",
      });

      // Feature block, for dev, Foundation Group, Foundation Group's client
      // instances, and the company's instances
      // if (
      // 	appConfig.instance === 'rickyvoltz'
      // 	|| appConfig.instance === 'voltzsoftware'
      // 	|| appConfig.instance === 'foundation_group'
      // 	|| appConfig.is_portal
      // ) {

      // 	rightTray = ui.makeNode('col4', 'div', {
      // 		css:'one wide column',
      // 		id:'rightTray'
      // 	});

      // 	rightTrayContainer = rightTray.makeNode('rightTrayContainer', 'div', {
      // 		id: 'rightTrayContainer',
      // 		style: 'margin:0 !important; text-align:center; overflow-y:scroll; height:100%; padding-top:18px; padding-bottom:90px;'
      // 	});
      // 	rightTrayContainer.makeNode('contextualRightTrayContainer', 'div', {});
      // 	rightTrayContainer.makeNode('myStuffRightTrayContainer', 'div', {});

      // 	var rightTrayBoxView = ui.makeNode('col5', 'div', {
      // 		style:'padding:0 !important;',
      // 		id:'rightTrayBoxView'
      // 	});

      // 	var rightTrayBoxViewContainer = rightTrayBoxView.makeNode('rightTrayBoxViewContainer', 'div', {
      // 		css:'rightTrayBoxViewContainer',
      // 	});

      // 	var closeRightTrayButton = rightTray.makeNode('closeRightTrayButton', 'div', {
      // 		text:'<i class="arrow right icon"></i>',
      // 		id:'closeRightTrayButton'
      // 	}).notify('click', {
      // 		type:'run-method',
      // 		data:{
      // 			run:function() {

      // 				sb.notify({
      // 					type:'close-right-tray'
      // 				});

      // 			}
      // 		}
      // 	}, sb.moduleId);

      // }

      // In portals, display the name of the instance at the top of the
      // main container area
      if (
        appConfig &&
        appConfig.rootInstance &&
        appConfig.rootInstance.is_portal === true
      ) {
        spaceViewCol.makeNode("i-h", "div", {
          tag: "h3",
          css: "ui center aligned header",
          text: "Welcome, " + appConfig.headquarters.name,
        });
        spaceViewCol.makeNode("i-h-br", "lineBreak", { spaces: 1 });
      }

      var mainContainerArea = spaceViewCol.makeNode("center", "div", {
        css: viewWidth + " wide column transition mainContainerArea",
        id: "mainContainerArea",
        style: "",
      });

      // var mobileRightTray = ui.makeNode('mobileRightTray', 'div', {
      // 	id: 'mobileRightTray',
      // 	css: 'ui sidebar right inverted vertical menu',
      // 	style:'background-color: #FFFFFF !important;'
      // });

      // var mobileRightTrayTopGrid = ui.makeNode('mobileRightTrayTopGrid', 'div', {
      // 	css: 'ui stackable grid',
      // 	style: 'position:fixed; background-color:#ffffff; margin:0; width:100%; z-index:999;'
      // });

      var menuControlIcon = '<i class="arrow left icon"></i>';
      if (spaceNavigationColOpen === false) {
        menuControlIcon = '<i class="arrow right icon"></i>';
      }

      var openMenuButton = spaceNavigationCol
        .makeNode("openMenuButton", "div", {
          text: menuControlIcon,
          css: "section ",
          id: "openMenuButton",
        })
        .notify(
          "click",
          {
            type: "run-method",
            data: {
              run: function () {
                if (spaceNavigationColOpen) {
                  // closed

                  sb.notify({
                    type: "close-toolBar",
                    data: {
                      state: state,
                    },
                  });
                } else {
                  // open

                  sb.notify({
                    type: "open-toolBar",
                    data: {
                      state: state,
                    },
                  });
                }
              },
            },
          },
          sb.moduleId
        );

      toolbarUI = spaceNavigationCol.makeNode("toolBar", "div", {
        css: "ui basic segment toolBar",
        style: "padding:0px;",
      });

      build_systemToolbar(toolbarUI);

      // ==================== //
      // === MOBILE HEADER == //
      // ==================== //

      var mobileHeader = spaceViewCol.makeNode("mobileHeader", "div", {
        id: "mobileHeader",
        style: "height:55px;",
      });
      mobileHeader.makeNode("openMobileNavBtn", "div", {
        id: "openMobileNavBtn",
        tag: "i",
        css: "bars icon",
      });

      var mobileHeaderLogoContainer = mobileHeader.makeNode(
        "logoContainer",
        "div",
        {
          style: "margin:0 auto;",
        }
      );
      var mobileHeaderLogoHelper = mobileHeaderLogoContainer.makeNode(
        "logoHelper",
        "div",
        {
          style: "display:inline-block; height:100%; vertical-align:middle;",
        }
      );
      var mobileHeaderLogo = mobileHeaderLogoContainer.makeNode("logo", "div", {
        style: "margin-top:6px; display:inline-block;",
      });
      viewLogo(mobileHeaderLogo, companyLogo);

      // mobileHeader.makeNode('openRightTrayBtn', 'div', {
      // 	id: 'openRightTrayBtn',
      // 	tag: 'i',
      // 	css: 'inbox icon'
      // });

      var mobileNav = ui.makeNode("mobileNav", "div", {
        id: "mobileNav",
        css: "ui sidebar inverted vertical menu",
        style: "background-color: #FFFFFF !important;",
      });

      var mobileNavTopGrid = ui.makeNode("mobileNavTopGrid", "div", {
        css: "ui stackable grid",
        style:
          "position:fixed; background-color:#ffffff; margin:0; width:100%; z-index:999;",
      });

      // Logo and close button
      var mobileNavTopNav = mobileNav.makeNode("mobileNav", "div", {
        id: "mobileNavTopNav",
        css: "sixteen wide column",
        style:
          "height:55px; padding-left:10px; border-bottom:1px solid #ebebeb; background-color:#ffffff; position:absolute; width:100%; z-index:999;",
      });
      var mobileNavLogoHelper = mobileNavTopNav.makeNode("logoHelper", "div", {
        style: "display:inline-block; height:100%; vertical-align:middle;",
      });
      var mobileNavLogo = mobileNavTopNav.makeNode("logo", "div", {
        style: "display:inline-block;",
      });
      viewLogo(mobileNavLogo, companyLogo);

      mobileNavTopNav.makeNode("closeMobileNavBtn", "div", {
        id: "closeMobileNavBtn",
        tag: "i",
        css: "times icon",
      });

      // ==================== //
      // ==================== //

      mainUI = ui;

      if (!refresh) {
        ui.makeNode("wrapper", "div", {});
        spaceViewCol.center.makeNode("seg1", "div", {});
        Header = spaceViewCol.center.seg1;
      }

      mainView_UI = spaceViewCol.center.makeNode("seg2", "div", {
        css: "ui basic segment mainViewContainer",
        style: "background-color: white !important;margin-top:0px; padding:0;",
        id: "mainViewContainer",
      });

      Main = spaceViewCol.center.seg2;

      main_ui(Main, Layers, urlData);

      spaceViewCol.center.makeNode("version", "div", {
        text:
          "<small>Powered by Bento Systems<br />" +
          PAGODA_APP_VERSION +
          "</small>",
        css: "ui center aligned grey tiny header",
      });

      spaceViewCol.center.makeNode("versionBreak", "div", { text: "<br />" });

      Modal = ui.makeNode("mc", "div", {}).makeNode("modal", "modal", {
        onClose: function () {
          ModalOnClose();
        },
      });

      Floater = ui.makeNode("floater", "div", {}).makeNode("modal", "div", {});

      ui.build();

      $(".versionDrop").dropdown({
        clearable: true,
      });

      refresh_page(doneCallback);

      // Do stuff after page is ready
      $(document).ready(function () {
        // Move tables based on screen size (on load)
        sb.notify({
          type: "move-elements-based-on-screen-size",
          data: {
            window: window,
          },
        });

        // Move tables based on screen size (on resize)
        $(window).on("resize", function () {
          var window = $(this);
          sb.notify({
            type: "move-elements-based-on-screen-size",
            data: {
              window: window,
            },
          });
        });

        // Initialize sidebar open/close buttons
        $(document).on(
          "click",
          "#openMobileNavBtn, #closeMobileNavBtn",
          function () {
            $("#mobileNav")
              .sidebar({
                context: $(".main"),
              })
              .sidebar("toggle");
          }
        );

        // Initialize right tray open/close buttons
        // $(document).on('click', '#openRightTrayBtn, #closeRightTrayBtn', function() {
        // 	$('#mobileRightTray').sidebar({
        // 		context: $('.main')
        // 	}).sidebar('toggle');
        // });

        $("#bottomGrid").append($("#rightTray"));
        $("#bottomGrid").append($("#rightTrayBoxView"));

        // Initialize sidebar close button
        $(document).on("click", "a.item", function () {
          $("#mobileMenu").sidebar("hide");
          $("#sidebarTopSection .ui.accordion").accordion("close", 0);
        });

        // Initialize sidebar accordion menu
        $("#sidebarTopSection .ui.accordion").accordion();

        // Show open/close sidebar button on hover
        $("#spaceNavigationCol").hover(
          function () {
            $("#openMenuButton").css("visibility", "visible");
          },
          function () {
            $("#openMenuButton").css("visibility", "hidden");
          }
        );
      });
    });
  }

  function viewLogo(topNav, companyLogo) {
    function build_uploadCompanyLogo(dom, setup) {
      // dom --> modal body
      // setup.modal --> modal dom obj
      // setup.mainDom

      var formObj = {
        upload: {
          name: "upload",
          label: "Choose file",
          type: "file-upload",
        },
      };
      var mainDom = setup.mainDom;

      function process_form(form, loading, after) {
        var formData = form.process().fields;
        var obj = {};

        function validate_form(data, callback) {
          if (data.upload.value.fileData === undefined) {
            sb.dom.alerts.alert("Error", "Please upload an image.", "error");

            callback(false);
          } else {
            callback(true);
          }
        }

        validate_form(formData, function (check) {
          if (!check) {
            return;
          } else {
            loading();

            obj.is_primary = "yes";
            obj.is_main_app_logo = "yes";
            obj.company_logo = formData.upload.value;

            sb.data.db.obj.create(
              "company_logo",
              obj,
              function (created) {
                after(created);
              },
              1
            );
          }
        });
      }

      dom.empty();

      dom.makeNode("wrapper", "div", {
        css: "animated fadeIn",
      });

      dom.wrapper.makeNode("head", "div", {});
      dom.wrapper.makeNode("lb1", "lineBreak", { spaces: 1 });
      dom.wrapper.makeNode("body", "div", {});

      dom.wrapper.makeNode("btnGrp", "div", {});

      dom.wrapper.head.makeNode("header", "div", {
        text: '<i class="upload icon"></i> Upload company logo',
        css: "ui header",
        tag: "h1",
      });

      dom.wrapper.body.makeNode("form", "form", formObj);

      dom.wrapper.btnGrp
        .makeNode("save", "div", {
          text: "Save",
          css: "ui green button right floated",
        })
        .notify(
          "click",
          {
            type: "run-method",
            data: {
              run: function (data) {
                process_form(
                  dom.wrapper.body.form,
                  function () {
                    dom.wrapper.body.empty();

                    dom.wrapper.body.makeNode("load_cont", "div", {
                      css: "text-center",
                    });
                    dom.wrapper.body.load_cont.makeNode("loader", "loader", {});
                    dom.wrapper.body.load_cont.makeNode("load_text", "div", {
                      text: "Uploading logo...",
                    });

                    dom.wrapper.body.patch();

                    dom.wrapper.btnGrp.empty();
                    dom.wrapper.btnGrp.patch();
                  },
                  function (created) {
                    if (created.is_primary === "yes") {
                      location.reload();
                    }

                    setup.modal.hide();
                  }
                );
              },
            },
          },
          sb.moduleId
        );

      dom.wrapper.btnGrp
        .makeNode("close", "div", {
          text: "Close",
          css: "ui red button right floated",
        })
        .notify(
          "click",
          {
            type: "run-method",
            data: {
              run: function (data) {
                setup.modal.hide();
              },
            },
          },
          sb.moduleId
        );

      dom.patch();
    }

    if (topNav.logo) {
      delete topNav.logo;
    }
    if (topNav.hqLogo_item) {
      delete topNav.hqLogo_item;
    }

    if (companyLogo) {
      var imgLink = "";
      if (appConfig.is_portal) {
        imgLink = "#p-" + Portals[0].id;
      } else {
        imgLink = window.location.href.split("#")[0] + "#mystuff";
      }

      topNav.makeNode("hqLogo_item", "div", {
        css: "item",
        style:
          "background: none !important; margin-bottom:0 !important; padding:0 !important;",
        tag: "a",
        href: imgLink,
      });
      topNav.hqLogo_item.makeNode("hqLogo", "div", {
        tag: "img",
        id: "navLogo",
        css: "ui centered image",
        style: "display:inline-block !important;",
        src: sb.data.files.getURL(companyLogo.company_logo),
      });
    }
  }

  // logout flow methods

  function logout(domObj, state, draw) {
    sb.notify({
      type: "get-accounts",
      data: {
        callback: function (accounts) {
          if (!_.isEmpty(accounts)) {
            accounts = _.reject(accounts, function (account) {
              return account.instance == sb.data.cookie.get("instance");
            });

            domObj
              .makeNode("panel", "div", { uiGrid: false })
              .makeNode("body", "div", { uiGrid: false });

            domObj.panel.body
              .makeNode("logout", "div", {
                text: 'Logout <i class="fa fa-sign-out"></i>',
                css: "ui red button",
                tag: "button",
              })
              .notify(
                "click",
                {
                  type: "logout-user",
                  data: {},
                },
                sb.moduleId
              );

            domObj.panel.body.makeNode("title", "div", {
              text: "Switch Accounts",
              css: "ui huge header",
            });

            //domObj.panel.body.makeNode('buttonBreak', 'div', {text:'<br />'});

            domObj.panel.body
              .makeNode("cont", "div", { uiGrid: false, css: "" })
              .makeNode("table", "table", {
                css: "ui striped fluid table",
                clearCSS: true,
                columns: {
                  instance: "Account",
                  btns: "",
                },
              });

            _.each(_.sortBy(accounts, "instance"), function (account) {
              var instanceName = account.instance.toUpperCase();
              if (!_.isEmpty(account.instanceName)) {
                instanceName = account.instanceName;
              }

              domObj.panel.body.cont.table.makeRow("instance-" + account.id, [
                instanceName,
                "",
              ]);

              domObj.panel.body.cont.table.body["instance-" + account.id].btns
                .makeNode("btn", "button", {
                  text: 'Login <i class="fa fa-arrow-right"></i>',
                  css: "pda-btn-green",
                })
                .notify(
                  "click",
                  {
                    type: "logout-user",
                    data: {
                      toAccount: account,
                      message:
                        "You are about to be logged out of this instance and log in to the " +
                        instanceName +
                        " instance.",
                    },
                  },
                  sb.moduleId
                );
            });

            draw(domObj);
          } else {
            domObj
              .makeNode("panel", "div", {
                css: "ui basic segment",
              })
              .makeNode("body", "div", {});

            domObj.panel.body.makeNode("title", "div", {
              tag: "h2",
              text: "Switch Accounts",
              css: "ui header",
            });

            domObj.panel.body
              .makeNode("logout", "div", {
                text: 'Logout <i class="fa fa-sign-out"></i>',
                css: "ui red button",
              })
              .notify(
                "click",
                {
                  type: "logout-user",
                  data: {},
                },
                sb.moduleId
              );

            domObj.panel.body.makeNode("lb_1", "lineBreak", { spaces: 1 });

            domObj.panel.body
              .makeNode("cont", "div", {})
              .makeNode("text", "div", {
                tag: "p",
                text: "No other Accounts Registered",
              });

            draw(domObj);
          }
        },
      },
    });
  }

  // end logout flow methods

  // welcome flow methods

  function projectTypesView(dom, loadSingle) {
    function editView(dom, type) {
      function saveProjectType(dom, projectType, callback) {
        var formData = dom.detailsForm.process().fields;

        if (projectType.hasOwnProperty("id")) {
          sb.data.db.obj.update(
            "project_types",
            {
              id: projectType.id,
              states: projectType.states,
              name: formData.name.value,
            },
            function (response) {
              callback(response);
            }
          );
        } else {
          sb.data.db.obj.create(
            "project_types",
            {
              states: projectType.states,
              name: formData.name.value,
            },
            function (response) {
              callback(response);
            }
          );
        }
      }

      function statesView(ui, states) {
        function displayStates(state) {
          var previousState = _.findWhere(states, {
            uid: parseInt(state.previous[0]),
          });
          var nextState = _.findWhere(states, { uid: parseInt(state.next[0]) });

          ui.makeNode("state-" + state.uid, "div", {
            css: "ui buttons",
          });

          // display
          stateBtn(ui["state-" + state.uid], undefined, undefined, state);

          // edit
          ui["state-" + state.uid]
            .makeNode("edit", "div", {
              css: "ui orange icon basic button",
              text: '<i class="pencil icon"></i>',
            })
            .notify(
              "click",
              {
                type: "projects-run",
                data: {
                  run: function (state, before, after) {
                    editStateModal(modal, state, before, after);
                  }.bind({}, state, previousState, nextState),
                },
              },
              sb.moduleId
            );

          ui.makeNode("br-" + state.uid + "-1", "div", { text: "<br />" });
          ui.makeNode("down-" + state.uid, "div", {
            tag: "i",
            css: "grey down arrow icon",
          });

          ui.makeNode("create-after" + state.uid, "div", {
            tag: "button",
            css: "ui mini green basic icon button",
            text: '<i class="plus icon"></i>',
          }).notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function (state, before, after) {
                  editStateModal(modal, state, before, after);
                }.bind({}, {}, state, nextState),
              },
            },
            sb.moduleId
          );

          ui.makeNode("up-" + state.uid, "div", {
            tag: "i",
            css: "grey up arrow icon",
          });
          ui.makeNode("br-" + state.uid + "-2", "div", { text: "<br />" });

          if (nextState) {
            displayStates(nextState);
          }
        }

        function editStateModal(modal, state, before, after) {
          function updateState(modal, state, callback) {
            var formData = modal.form.process().fields;
            var newColor = $(modal.colorSelector.selector).dropdown(
              "get value"
            );
            if (Array.isArray(newColor)) {
              newColor = newColor.pop();
            }
            if (typeof newColor === "string") {
              newColor = newColor.split(">").pop();
            }
            var newIcon = $(modal.iconSelector.selector).dropdown("get value");
            if (Array.isArray(newIcon)) {
              newIcon = newIcon.pop();
            }
            if (typeof newIcon === "string") {
              newIcon = newIcon.split(">").pop();
            }

            state.name = formData.name.value;
            if (newColor) {
              state.color = newColor;
            }
            if (newIcon) {
              state.icon = newIcon;
            }

            modal.empty();
            modal.patch();

            callback(state);
          }

          var create = !state.hasOwnProperty("uid");

          modal.body.empty();
          modal.footer.empty();

          // details form
          var formArgs = {
            name: {
              name: "name",
              type: "text",
              label: "Workflow Name",
              placeholder: "In progress, Complete, etc.",
              value: state.name || "",
            },
          };

          modal.body.makeNode("form", "form", formArgs);
          modal.body.makeNode("br", "div", { text: "<br />" });

          // color selection
          var currentColor = state.color || "Status color";
          var colorSelector = modal.body.makeNode("colorSelector", "div", {
            css: "ui floating dropdown labeled search icon basic button",
            listener: {
              type: "dropdown",
              maxSelections: 1,
            },
          });
          colorSelector.makeNode("icon", "div", {
            tag: "i",
            css: "paint brush icon",
          });
          colorSelector.makeNode("text", "div", {
            tag: "span",
            css: "text",
            text: currentColor,
          });

          colorSelector.makeNode("menu", "div", { css: "menu" });

          var colors = sb.dom.colors;
          _.each(colors, function (color, i) {
            colorSelector.menu.makeNode("option-" + i, "div", {
              css: "item",
              text:
                '<div class="ui ' +
                color +
                ' empty circular label"></div>' +
                color,
            });
          });

          // icon selection
          var iconSelector = modal.body.makeNode("iconSelector", "div", {
            css: "ui floating dropdown labeled search icon basic button",
            listener: {
              type: "dropdown",
              maxSelections: 1,
            },
          });
          var currentIcon = "Status Icon";
          if (state.icon) {
            currentIcon =
              '<i class="ui ' + state.icon + ' icon"></i>' + state.icon;
          }
          iconSelector.makeNode("icon", "div", {
            tag: "i",
            css: "circle outline icon",
          });
          iconSelector.makeNode("text", "div", {
            tag: "span",
            css: "text",
            text: currentIcon,
          });

          iconSelector.makeNode("menu", "div", { css: "menu" });

          var icons = sb.dom.icons;
          _.each(
            _.sortBy(icons, function (icon) {
              return icon.name;
            }),
            function (icon, i) {
              iconSelector.menu.makeNode("option-" + i, "div", {
                css: "item",
                text: '<i class="ui ' + icon.name + ' icon"></i>' + icon.name,
              });
            }
          );

          // remove btn
          if (!create && states.length > 1) {
            modal.footer
              .makeNode("removeBtn", "div", {
                text: "Remove",
                css: "ui left floated red button",
                tag: "button",
              })
              .notify(
                "click",
                {
                  type: "projects-run",
                  data: {
                    run: function () {
                      if (before) {
                        before.next = state.next.slice(0);
                      }
                      if (after) {
                        after.previous = state.previous.slice(0);
                        if (state.isEntryPoint) {
                          after.isEntryPoint = 1;
                        }
                      }
                      states = _.filter(states, function (s) {
                        return s.uid !== state.uid;
                      });

                      modal.hide();

                      statesView(ui, states);
                      ui.patch();
                    },
                  },
                },
                sb.moduleId
              );
          }

          // update btn
          modal.footer
            .makeNode("update", "div", {
              tag: "button",
              text: "Update",
              css: "ui teal right floated button",
            })
            .notify(
              "click",
              {
                type: "projects-run",
                data: {
                  run: function () {
                    modal.footer.update.loading();
                    updateState(modal.body, state, function (newState) {
                      modal.hide();

                      if (create) {
                        var nextId = _.reduce(
                          states,
                          function (memo, state) {
                            if (state.uid >= memo) {
                              return state.uid + 1;
                            } else {
                              return memo;
                            }
                          },
                          0
                        );

                        newState.uid = nextId;

                        if (before && before.uid) {
                          before.next = [nextId];
                          newState.previous = [before.uid];
                        } else {
                          newState.previous = [];
                        }
                        if (after && after.uid) {
                          after.previous = [nextId];
                          newState.next = [after.uid];
                          if (after.isEntryPoint) {
                            delete after.isEntryPoint;
                            newState.isEntryPoint = 1;
                          }
                        } else {
                          newState.next = [];
                        }

                        states.push(newState);
                        statesView(ui, states);
                        ui.patch();
                      } else {
                        // display
                        stateBtn(
                          ui["state-" + state.uid],
                          undefined,
                          undefined,
                          state
                        );
                        ui["state-" + state.uid].patch();
                      }
                    });
                  }.bind({}, state),
                },
              },
              sb.moduleId
            );

          modal.body.patch();
          modal.footer.patch();

          modal.show();
        }

        ui.empty();

        ui.makeNode("header", "div", {
          tag: "h4",
          css: "ui header",
          text: 'Project flow<div class="sub header">Define this workflow by setting the possible states.</div>',
        });

        var modal = ui.makeNode("editModal", "modal", {
          size: "small",
          bodyStyle: "min-height:500px;",
        });
        var firstState = _.findWhere(states, { isEntryPoint: 1 });

        ui.makeNode("create-before-" + firstState.uid, "div", {
          tag: "button",
          css: "ui mini green basic icon button",
          text: '<i class="plus icon"></i>',
        }).notify(
          "click",
          {
            type: "projects-run",
            data: {
              run: function (after) {
                editStateModal(modal, {}, {}, firstState);
              }.bind({}, firstState),
            },
          },
          sb.moduleId
        );

        ui.makeNode("br-first", "div", { text: "<br />" });

        displayStates(firstState);
      }

      var create = _.isEmpty(type);

      // default values
      if (create) {
        type.states = [
          {
            uid: 1,
            name: "Planning",
            icon: "",
            previous: [],
            next: [2],
            isEntryPoint: 1,
          },
          {
            uid: 2,
            name: "In progress",
            icon: "",
            previous: [1],
            next: [3],
          },
          {
            uid: 3,
            name: "Done",
            icon: "",
            previous: [2],
            next: [],
          },
        ];
      }

      dom.empty();

      // details form
      var formArgs = {
        name: {
          name: "name",
          type: "text",
          label: "Name",
          value: type.name || "Regular Workflow",
          placeholder: "Design, contract, etc.",
        },
      };

      dom.makeNode("detailsForm", "form", formArgs);

      // states
      dom.makeNode("br1", "div", { text: "<br />" });
      dom.makeNode("states", "div", { css: "text-center" });

      statesView(dom.states, type.states);

      // buttons
      dom.makeNode("br2", "div", { text: "<br />" });
      dom
        .makeNode("btns", "div", {
          css: "ui buttons",
        })
        .makeNode("save", "button", {
          css: "ui green button",
          text: "Save",
        })
        .notify(
          "click",
          {
            type: "projects-run",
            data: {
              run: function () {
                dom.btns.save.loading();
                saveProjectType(dom, type, function (response) {
                  if (response) {
                    sb.dom.alerts.alert("Saved!", "", "success");
                    editView(dom, type);
                  } else {
                    sb.dom.alerts.alert(
                      "Oops!",
                      "Something went wrong -- refresh and try again.",
                      "error"
                    );

                    dom.btns.save.loading(false);
                  }
                });
              },
            },
          },
          sb.moduleId
        );

      dom.btns
        .makeNode("back", "div", {
          css: "ui orange button",
          text: "Cancel",
        })
        .notify(
          "click",
          {
            type: "projects-run",
            data: {
              run: function () {
                projectTypesView(dom);
              },
            },
          },
          sb.moduleId
        );

      if (!create) {
        dom.btns
          .makeNode("delete", "button", {
            css: "ui red button",
            text: "Delete",
          })
          .notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function () {
                  //!TODO: check if projects are using this type->only archive if they are
                  sb.dom.alerts.ask(
                    {
                      title: "Are you sure?",
                      text: "",
                    },
                    function (resp) {
                      if (resp) {
                        dom.btns.delete.loading(true);
                        sb.data.db.obj.erase(
                          "project_types",
                          type.id,
                          function (response) {
                            if (response) {
                              sb.dom.alerts.alert("Deleted!", "", "success");
                              projectTypesView(dom);
                            } else {
                              sb.dom.alerts.alert(
                                "Oops!",
                                "An error occurred -- please refresh and try again.",
                                "error"
                              );
                            }
                          }
                        );
                      }
                    }
                  );
                },
              },
            },
            sb.moduleId
          );
      }

      dom.patch();

      if (create) {
        saveProjectType(dom, type, function (response) {});
      }
    }

    if (loadSingle.object_bp_type) {
      editView(dom, loadSingle);
    } else {
      sb.data.db.obj.getAll(
        "project_types",
        function (project_types) {
          if (loadSingle.object_bp_type) {
            editView(dom, project_types[0]);
          } else {
            if (project_types.length == 0) {
              editView(dom, {});
            } else {
              dom.empty();

              dom
                .makeNode("btns", "div", {
                  css: "ui right floated buttons",
                })
                .makeNode("create", "div", {
                  tag: "button",
                  css: "ui green button",
                  text: "New project type",
                })
                .notify(
                  "click",
                  {
                    type: "projects-run",
                    data: {
                      run: function () {
                        editView(dom, {});
                      },
                    },
                  },
                  sb.moduleId
                );

              // table
              dom.makeNode("br-before-table", "div", { text: "<br /><br />" });
              dom
                .makeNode("table", "div", {
                  tag: "table",
                  css: "ui basic table",
                })
                .makeNode("thead", "div", { tag: "thead" });
              dom.table.thead
                .makeNode("tr", "div", { tag: "tr" })
                .makeNode("name", "div", { tag: "th", text: "Name" });
              dom.table.thead.tr.makeNode("btns", "div", { tag: "th" });
              dom.table.makeNode("body", "div", { tag: "tbody" });

              _.each(project_types, function (project_type) {
                // type name
                dom.table.body
                  .makeNode("type-" + project_type.id, "div", { tag: "tr" })
                  .makeNode("name", "div", {
                    tag: "td",
                    text: project_type.name,
                  });

                // type link
                dom.table.body["type-" + project_type.id]
                  .makeNode("btns", "div", { tag: "td" })
                  .makeNode("open", "div", {
                    tag: "button",
                    css: "ui tiny yellow button",
                    text: "Edit",
                  })
                  .notify(
                    "click",
                    {
                      type: "projects-run",
                      data: {
                        run: function (typeObj) {
                          editView(dom, typeObj);
                        }.bind({}, project_type),
                      },
                    },
                    sb.moduleId
                  );
              });

              dom.patch();
            }
          }
        },
        {
          name: true,
          states: true,
        }
      );
    }
  }

  function setupProjectTypes(callback) {
    sb.data.db.obj.getAll("project_types", function (projectTypes) {
      if (projectTypes.length > 0) {
        callback(projectTypes[0]);
      } else {
        callback({});
      }
    });
  }

  function stateBtn(ui, obj, status, state, patch, fluid, callback) {
    function getColor(state) {
      var color = "";
      if (!_.isEmpty(state.color)) {
        color = state.color + " ";
      }
      return color;
    }

    function getIcon(state) {
      var icon = {
        css: "",
        text: "",
      };

      if (!_.isEmpty(state.icon)) {
        icon.css = "icon ";
        icon.text = '<i class="' + state.icon + ' icon"></i>';
      }

      return icon;
    }

    function updateProjectState(obj, newState, callback) {
      sb.data.db.obj.update(
        "projects",
        {
          id: obj.id,
          state: newState.uid,
        },
        function (updatedObj) {
          obj.state = updatedObj.state;
          callback(obj);
        }
      );
    }

    var showSelection = obj !== undefined;

    if (obj) {
      state = _.findWhere(status, { id: parseInt(obj[state]) });
      if (!state) {
        state = _.findWhere(status, { isEntryPoint: 1 });
      }
    }

    var color = getColor(state);
    var icon = getIcon(state);

    if (showSelection) {
      var btnCSS = "ui " + color + "dropdown button";

      if (fluid) {
        btnCSS += " fluid";
      }

      ui.makeNode("obj-" + obj.id + "-state-btn", "div", {
        text: "<nobr>" + icon.text + " " + state.name + "</nobr> ",
        css: btnCSS,
        tag: "button",
        listener: {
          type: "dropdown",
        },
      }).makeNode("menu", "div", {
        css: "menu",
      });

      var previousState = _.findWhere(status, {
        uid: parseInt(state.previous[0]),
      });
      var nextState = _.findWhere(status, { uid: parseInt(state.next[0]) });

      if (nextState) {
        color = getColor(nextState);
        icon = getIcon(nextState);

        ui["obj-" + obj.id + "-state-btn"].menu
          .makeNode("next", "div", {
            css: "ui item fluid " + color + "button",
            text:
              "move forward to <strong>" +
              icon.text +
              nextState.name +
              "</strong>",
            style: "border-radius:0px !important;",
          })
          .notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function () {
                  ui["obj-" + obj.id + "-state-btn"].loading();
                  updateProjectState(obj, nextState, function (updatedObj) {
                    stateBtn(
                      ui,
                      updatedObj,
                      updatedObj.type.states,
                      "state",
                      true,
                      fluid
                    );
                    if (callback) {
                      callback(true);
                    }
                  });
                },
              },
            },
            sb.moduleId
          );
      }

      if (previousState) {
        color = getColor(previousState);
        icon = getIcon(previousState);

        ui["obj-" + obj.id + "-state-btn"].menu
          .makeNode("prev", "div", {
            css: "ui item fluid " + color + "button",
            text:
              "move back to <strong>" +
              icon.text +
              previousState.name +
              "</strong>",
            style: "border-radius:0px !important;",
          })
          .notify(
            "click",
            {
              type: "projects-run",
              data: {
                run: function () {
                  ui["obj-" + obj.id + "-state-btn"].loading();
                  updateProjectState(obj, previousState, function (updatedObj) {
                    stateBtn(
                      ui,
                      updatedObj,
                      updatedObj.type.states,
                      "state",
                      true,
                      fluid
                    );
                    if (callback) {
                      callback(true);
                    }
                  });
                },
              },
            },
            sb.moduleId
          );
      }
    } else {
      ui.makeNode("b-" + state.uid, "div", {
        text: icon.text + " " + state.name,
        tag: "button",
        css: "ui " + color + icon.css + "button",
      });
    }
    if (patch) {
      ui.patch();
    }
  }

  function welcomeUser(mainDom, state, draw) {
    function header(step) {
      var dom = this;
      var welcome = "disabled ";
      var hq = "disabled ";
      var tips = "disabled ";
      var teams = "disabled ";
      var workflows = "disabled ";
      var people = "disabled ";

      switch (step) {
        case "Welcome":
          welcome = "active ";

          break;

        case "HQ":
          tips = "completed ";
          hq = "active ";

          break;

        case "Tips":
          welcome = "completed ";
          //hq = 'completed ';
          tips = "active ";

          break;

        case "Teams":
          welcome = "completed ";
          hq = "completed ";
          teams = "active ";

          break;

        case "Workflows":
          welcome = "completed ";
          hq = "completed ";
          teams = "completed ";
          workflows = "active ";

          break;

        case "People":
          welcome = "completed ";
          hq = "completed ";
          teams = "completed ";
          workflows = "completed ";
          people = "active ";

          break;
      }

      dom
        .makeNode("grid", "div", { css: "ui stackable centered grid" })
        .makeNode("col", "div", { css: "nine wide column" });

      dom.grid.col.makeNode("steps", "div", {
        css: "ui two ordered fluid steps",
      });

      /*
			  dom.grid.col.steps.makeNode('welcome', 'div', {css:welcome+'step'})
				  .makeNode('content', 'div', {css:'content'});
			  dom.grid.col.steps.welcome.content.makeNode('title', 'div', {css:'title', text:'Welcome'});
			  dom.grid.col.steps.welcome.content.makeNode('description', 'div', {css:'description', text:'Get Started'});
  */

      dom.grid.col.steps
        .makeNode("tips", "div", { css: tips + "step" })
        .makeNode("content", "div", { css: "content" });
      dom.grid.col.steps.tips.content.makeNode("title", "div", {
        css: "title",
        text: "Quick Tips",
      });
      dom.grid.col.steps.tips.content.makeNode("description", "div", {
        css: "description",
        text: "Some quick tips before you get started.",
      });

      dom.grid.col.steps
        .makeNode("hq", "div", { css: hq + "step" })
        .makeNode("content", "div", { css: "content" });
      dom.grid.col.steps.hq.content.makeNode("title", "div", {
        css: "title",
        text: "Basic Setup",
      });
      dom.grid.col.steps.hq.content.makeNode("description", "div", {
        css: "description",
        text: "Name your HQ and select a business type",
      });

      /*
			  dom.grid.col.steps.makeNode('workflows', 'div', {css:workflows+'step'})
				  .makeNode('content', 'div', {css:'content'});
			  dom.grid.col.steps.workflows.content.makeNode('title', 'div', {css:'title', text:'Workflows'});
			  dom.grid.col.steps.workflows.content.makeNode('description', 'div', {css:'description', text:'Setup <i>how</i> you work'});

			  dom.grid.col.steps.makeNode('people', 'div', {css:people+'step'})
				  .makeNode('content', 'div', {css:'content'});
			  dom.grid.col.steps.people.content.makeNode('title', 'div', {css:'title', text:'People'});
			  dom.grid.col.steps.people.content.makeNode('description', 'div', {css:'description', text:'Add people to your account'});
  */

      dom.patch();
    }

    function intro(dom, steps, setupObj) {
      dom
        .makeNode("grid", "div", {
          css: "ui stackable centered grid animated fadeIn",
        })
        .makeNode("col", "div", { css: "nine wide column" });

      dom.grid.col
        .makeNode("title", "div", { css: "ui huge header" })
        .makeNode("title", "div", {
          text: "Welcome to Bento Systems",
          css: "ui header",
        });

      dom.grid.col.makeNode("start1", "div", {
        css: "ui large header",
        text: "We need to setup a few things before you get started.",
      });

      dom.grid.col.makeNode("start2", "div", {
        css: "ui huge header",
        text: "1. Headquarters",
      });
      dom.grid.col.makeNode("title2", "div", {
        css: "",
        style: "font-size:1.2em;",
        text: "Your starting point. All of your projects and teams are located here.",
      });
      dom.grid.col.makeNode("start3", "div", {
        css: "ui huge header",
        text: "2. Teams",
      });
      dom.grid.col.makeNode("title3", "div", {
        css: "",
        style: "font-size:1.2em;",
        text: "Organize people into easy to understand groups.",
      });
      dom.grid.col.makeNode("start4", "div", {
        css: "ui huge header",
        text: "3. Workflows",
      });
      dom.grid.col.makeNode("title4", "div", {
        css: "",
        style: "font-size:1.2em;",
        text: "Setup different project types and the workflows they move through.",
      });
      dom.grid.col.makeNode("start5", "div", {
        css: "ui huge header",
        text: "4. People",
      });
      dom.grid.col.makeNode("title5", "div", {
        css: "",
        style: "font-size:1.2em;",
        text: "Invite people to your Headquarters. Once they accept, they can be added to Teams and participate in Projects.",
      });

      dom.grid.col.makeNode("titleBreak", "div", { text: "<br />" });

      dom.grid.col
        .makeNode("go", "div", {
          css: "ui green centered huge button",
          text: "Get started",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom, steps, setupObj) {
                steps("HQ");

                headquarters(dom, steps, setupObj);
              }.bind({}, dom, steps, setupObj),
            },
          },
          sb.moduleId
        );

      dom.patch();
    }

    function headquarters(dom, steps, setupObj) {
      window.scrollTo(0, 0);

      dom.empty();

      dom
        .makeNode("grid", "div", {
          css: "ui stackable centered grid animated fadeIn",
        })
        .makeNode("col", "div", { css: "ten wide column" });

      dom.grid.col.makeNode("titlebreak", "div", {
        css: "ui horizontal divider",
        text: "name your headquarters",
      });
      dom.grid.col.makeNode("formCont", "div", {
        css: "ui basic padded segment",
      });
      dom.grid.col.formCont.makeNode("hqForm", "form", {
        name: {
          label: "",
          name: "name",
          type: "text",
          placeholder: "Your Organization's Name",
          fieldCSS: "ui massive inverted fluid input",
        },
      });

      dom.grid.col.makeNode("break1", "div", { text: "<br />" });
      dom.grid.col.makeNode("break", "div", {
        css: "ui horizontal divider",
        text: "choose a business type",
      });

      dom.grid.col.makeNode("selCont", "div", { css: "ui basic segment" });
      dom.grid.col.selCont.makeNode("templates", "div", {
        css: "ui padded segment",
      });
      dom.grid.col.selCont.templates.makeNode("cont", "div", {
        css: "ui two column very relaxed stackable grid",
      });
      dom.grid.col.selCont.templates.cont.makeNode("one", "div", {
        css: "column",
      });
      //dom.grid.col.selCont.templates.cont.makeNode('two', 'div', {css:'column'});
      dom.grid.col.selCont.templates.cont.makeNode("three", "div", {
        css: "column",
      });

      var one = dom.grid.col.selCont.templates.cont.one;
      //var two = dom.grid.col.selCont.templates.cont.two;
      var three = dom.grid.col.selCont.templates.cont.three;

      one.makeNode("title", "div", {
        css: "ui large header",
        text: "Standard",
      });
      one.makeNode("titlediv", "div", { css: "ui divider" });
      one.makeNode("message", "div", { css: "ui basic segment" });
      one.message.makeNode("title", "div", {
        css: "ui small header",
        text: "What will be created?",
      });
      one.message.makeNode("list", "div", { css: "ui relaxed bulleted list" });
      one.message.list.makeNode("item1", "div", {
        css: "item",
        text: "Sales, Marketing, and Admin teams.",
      });
      one.message.list.makeNode("item2", "div", {
        css: "item",
        text: "Contact types like 'Client' and 'Lead'.",
      });
      one.message.list.makeNode("item3", "div", {
        css: "item",
        text: "Access to all of the tools available in Bento.",
      });
      one.makeNode("btn", "div", {
        css: "ui right labeled green icon button",
        text: 'Select <i class="right arrow icon"></i>',
      });

      /*
			  two.makeNode('title', 'div', {css:'ui large header', text:'Sign & Graphics'});
			  two.makeNode('titlediv', 'div', {css:'ui divider'});
			  two.makeNode('message', 'div', {css:'ui basic segment'});
			  two.message.makeNode('title', 'div', {css:'ui small header', text:'What will be created?'});
			  two.message.makeNode('list', 'div', {css:'ui relaxed bulleted list'});
			  two.message.list.makeNode('item1', 'div', {css:'item', text:'Sales, Marketing, Admin, and Inventory teams.'});
			  two.message.list.makeNode('item2', 'div', {css:'item', text:'Contact types like \'Client\' and \'Lead\'.'});
			  two.message.list.makeNode('item3', 'div', {css:'item', text:'Access to all of the tools available in Bento.'});
			  two.makeNode('btn', 'div', {css:'ui centered green button', text:'Select'});
  */

      three.makeNode("title", "div", {
        css: "ui large header",
        text: "Non-profit",
      });
      three.makeNode("titlediv", "div", { css: "ui divider" });
      three.makeNode("message", "div", { css: "ui basic segment" });
      three.message.makeNode("title", "div", {
        css: "ui small header",
        text: "What will be created?",
      });
      three.message.makeNode("list", "div", {
        css: "ui relaxed bulleted list",
      });
      three.message.list.makeNode("item1", "div", {
        css: "item",
        text: "Board of Director, Fundraising, and Recruiting teams.",
      });
      three.message.list.makeNode("item2", "div", {
        css: "item",
        text: "Contact types like 'Donor' and 'Volunteer'.",
      });
      three.message.list.makeNode("item3", "div", {
        css: "item",
        text: "Access to all of the tools available in Bento.",
      });
      three.makeNode("btn", "div", {
        css: "ui right labeled green icon button",
        text: 'Select <i class="right arrow icon"></i>',
      });

      one.btn.notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (dom, steps, setupObj) {
              var formData = dom.grid.col.formCont.hqForm.process().fields;

              if (formData.name.value == "") {
                sb.dom.alerts.alert(
                  "Error",
                  "Please enter a company name for your account.",
                  "error"
                );

                return;
              }

              one.btn.loading();
              three.btn.loading();

              setupObj.instanceName = formData.name.value;
              setupObj.instanceSetupType = "standard";

              setup(setupObj, function (done) {
                /*
							  ga('send', {
								  hitType: 'event',
								  eventCategory: 'Accounts',
								  eventAction: 'Setup',
								  eventLabel:setupObj.instanceSetupType
							  });
  */

                if (_gs) {
                  _gs("event", "Account Setup");
                }

                location.reload();
              });
            }.bind({}, dom, steps, setupObj),
          },
        },
        sb.moduleId
      );

      /*
			  two.btn.notify('click', {
					  type:'headquarters-run',
					  data:{
						  run:function(dom, steps, setupObj){

							  var formData = dom.grid.col.formCont.hqForm.process().fields;

							  if(formData.name.value == ''){

								  sb.dom.alerts.alert('Error', 'Please enter a company name for your account.', 'error');

								  return;

							  }

							  one.btn.loading();
							  two.btn.loading();
							  three.btn.loading();

							  setupObj.instanceName = formData.name.value;
							  setupObj.instanceSetupType = 'sign-graphics';

							  setup(setupObj, function(done){

								  ga('send', {
									  hitType: 'event',
									  eventCategory: 'Accounts',
									  eventAction: 'Setup',
									  eventLabel:setupObj.instanceSetupType
								  });

								  location.reload();

							  });

						  }.bind({}, dom, steps, setupObj)
					  }
				  }, sb.moduleId);
  */

      three.btn.notify(
        "click",
        {
          type: "headquarters-run",
          data: {
            run: function (dom, steps, setupObj) {
              var formData = dom.grid.col.formCont.hqForm.process().fields;

              if (formData.name.value == "") {
                sb.dom.alerts.alert(
                  "Error",
                  "Please enter a company name for your account.",
                  "error"
                );

                return;
              }

              one.btn.loading();
              three.btn.loading();

              setupObj.instanceName = formData.name.value;
              setupObj.instanceSetupType = "non-profit";

              setup(setupObj, function (done) {
                /*
								  ga('send', {
									  hitType: 'event',
									  eventCategory: 'Accounts',
									  eventAction: 'Setup',
									  eventLabel:setupObj.instanceSetupType
								  });
  */

                if (_gs) {
                  _gs("event", "Account Setup");
                }

                location.reload();
              });
            }.bind({}, dom, steps, setupObj),
          },
        },
        sb.moduleId
      );

      dom.patch();
    }

    function people(dom, steps, setupObj) {
      dom.empty();

      dom
        .makeNode("grid", "div", {
          css: "ui stackable centered grid animated fadeIn",
        })
        .makeNode("col", "div", { css: "thirteen wide column" });

      dom.grid.col
        .makeNode("title", "div", { css: "ui huge header" })
        .makeNode("title", "div", { text: "People", css: "ui header" });

      dom.grid.col.makeNode("start", "div", {
        css: "ui header",
        text: "Invite some people to your Headquarters.",
      });
      dom.grid.col.makeNode("title1", "div", {
        css: "",
        style: "font-size:1.2em;",
        text: "After they log in, they will be able to join teams and create projects. You can skip this step and add people later.",
      });

      var peopleCount = 1;

      dom.grid.col.makeNode("peopleCont", "div", {});
      dom.grid.col.peopleCont.makeNode("peopleGrid" + peopleCount, "div", {
        css: "ui horizontal stackable segments",
      });
      dom.grid.col.peopleCont["peopleGrid" + peopleCount]
        .makeNode("col1", "div", { css: "ui blue segment" })
        .makeNode("fname", "form", {
          field: {
            name: "field",
            type: "text",
            label: "First Name",
            placeholder: "First Name",
          },
        });
      dom.grid.col.peopleCont["peopleGrid" + peopleCount]
        .makeNode("col2", "div", { css: "ui blue segment" })
        .makeNode("lname", "form", {
          field: {
            name: "field",
            type: "text",
            label: "Last Name",
            placeholder: "Last Name",
          },
        });
      dom.grid.col.peopleCont["peopleGrid" + peopleCount]
        .makeNode("col3", "div", { css: "ui blue segment" })
        .makeNode("email", "form", {
          field: {
            name: "field",
            type: "text",
            label: "Email Address",
            placeholder: "<EMAIL>",
          },
        });
      dom.grid.col.peopleCont["peopleGrid" + peopleCount]
        .makeNode("col4", "div", { css: "ui blue segment" })
        .makeNode("phone", "form", {
          field: {
            name: "field",
            type: "text",
            label: "Cell Phone",
            placeholder: "************",
          },
        });

      dom.grid.col
        .makeNode("newPerson", "div", {
          text: "Add another person",
          tag: "button",
          css: "ui huge button",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom) {
                peopleCount++;

                dom.grid.col.peopleCont.makeNode(
                  "peopleGrid" + peopleCount,
                  "div",
                  { css: "ui horizontal segments" }
                );

                dom.grid.col.peopleCont["peopleGrid" + peopleCount]
                  .makeNode("col1", "div", { css: "ui blue segment" })
                  .makeNode("fname", "form", {
                    field: {
                      name: "field",
                      type: "text",
                      label: "First Name",
                      placeholder: "First Name",
                    },
                  });
                dom.grid.col.peopleCont["peopleGrid" + peopleCount]
                  .makeNode("col2", "div", { css: "ui blue segment" })
                  .makeNode("lname", "form", {
                    field: {
                      name: "field",
                      type: "text",
                      label: "Last Name",
                      placeholder: "Last Name",
                    },
                  });
                dom.grid.col.peopleCont["peopleGrid" + peopleCount]
                  .makeNode("col3", "div", { css: "ui blue segment" })
                  .makeNode("email", "form", {
                    field: {
                      name: "field",
                      type: "text",
                      label: "Email Address",
                      placeholder: "<EMAIL>",
                    },
                  });
                dom.grid.col.peopleCont["peopleGrid" + peopleCount]
                  .makeNode("col4", "div", { css: "ui blue segment" })
                  .makeNode("phone", "form", {
                    field: {
                      name: "field",
                      type: "text",
                      label: "Cell Phone",
                      placeholder: "************",
                    },
                  });

                dom.grid.col.peopleCont.patch();
              }.bind({}, dom),
            },
          },
          sb.moduleId
        );

      //dom.grid.col.makeNode('peopleBreak', 'div', {text:'<br />'});

      dom.grid.col
        .makeNode("skip", "div", {
          css: "ui blue huge button",
          text: "Skip and complete setup",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom, setupObj) {
                var people = [];

                if (peopleCount > 0) {
                  var looping = 1;
                  while (looping <= peopleCount) {
                    people.push({
                      fname:
                        dom.grid.col.peopleCont[
                          "peopleGrid" + looping
                        ].col1.fname.process().fields.field.value,
                      lname:
                        dom.grid.col.peopleCont[
                          "peopleGrid" + looping
                        ].col2.lname.process().fields.field.value,
                      email:
                        dom.grid.col.peopleCont[
                          "peopleGrid" + looping
                        ].col3.email.process().fields.field.value,
                      phone:
                        dom.grid.col.peopleCont[
                          "peopleGrid" + looping
                        ].col4.phone.process().fields.field.value,
                    });

                    looping++;
                  }
                }

                setupObj.people = people;
                hqTools = [];
                var toolOrder = 0;
                _.each(
                  _.sortBy(appConfig.hqTools, "name"),
                  function (toolToAdd) {
                    hqTools.push({
                      allowed_users: [+sb.data.cookie.userId],
                      system_name: toolToAdd.id,
                      display_name: toolToAdd.name,
                      is_archieved: 0,
                      order: toolOrder,
                      added_by: +sb.data.cookie.userId,
                      added_on: moment(),
                      settings: {},
                      box_color: toolToAdd.icon.color,
                    });

                    toolOrder++;
                  }
                );

                var hq = {
                  name: setupObj.instanceName,
                  parent: 0,
                  group_type: "Headquarters",
                  allowed_users: [+sb.data.cookie.userId],
                  managers: [+sb.data.cookie.userId],
                  tools: hqTools,
                };

                sb.data.db.obj.create("groups", hq, function (newHQ) {
                  var teams = [];
                  _.each(setupObj.teams, function (teamName) {
                    if (teamName != "") {
                      teamTools = [];
                      var toolOrder = 0;
                      _.each(
                        _.sortBy(appConfig.teamTools, "name"),
                        function (toolToAdd) {
                          teamTools.push({
                            allowed_users: [+sb.data.cookie.userId],
                            system_name: toolToAdd.id,
                            display_name: toolToAdd.name,
                            is_archieved: 0,
                            order: toolOrder,
                            added_by: +sb.data.cookie.userId,
                            added_on: moment(),
                            settings: {},
                            box_color: toolToAdd.icon.color,
                          });

                          toolOrder++;
                        }
                      );

                      teams.push({
                        name: teamName,
                        group_type: "Team",
                        allowed_users: [+sb.data.cookie.userId],
                        managers: [+sb.data.cookie.userId],
                        tagged_with: [0],
                        parent: hq.id,
                        tools: teamTools,
                      });
                    }
                  });

                  sb.data.db.obj.create(
                    "note_types",
                    {
                      name: "General",
                      default: "yes",
                    },
                    function (r) {
                      if (teams.length > 0) {
                        sb.data.db.obj.create(
                          "groups",
                          teams,
                          function (newTeams) {
                            location.reload();
                          }
                        );
                      } else {
                        location.reload();
                      }
                    }
                  );
                });
              }.bind({}, dom, setupObj),
            },
          },
          sb.moduleId
        );

      dom.grid.col
        .makeNode("continue", "div", {
          css: "ui green huge button",
          text: "Complete setup",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom, setupObj) {
                var people = [];

                if (peopleCount > 0) {
                  var looping = 1;
                  while (looping <= peopleCount) {
                    people.push({
                      fname:
                        dom.grid.col.peopleCont[
                          "peopleGrid" + looping
                        ].col1.fname.process().fields.field.value,
                      lname:
                        dom.grid.col.peopleCont[
                          "peopleGrid" + looping
                        ].col2.lname.process().fields.field.value,
                      email:
                        dom.grid.col.peopleCont[
                          "peopleGrid" + looping
                        ].col3.email.process().fields.field.value,
                      phone:
                        dom.grid.col.peopleCont[
                          "peopleGrid" + looping
                        ].col4.phone.process().fields.field.value,
                    });

                    looping++;
                  }
                }

                setupObj.people = people;
                hqTools = [];
                var toolOrder = 0;
                _.each(
                  _.sortBy(appConfig.hqTools, "name"),
                  function (toolToAdd) {
                    hqTools.push({
                      allowed_users: [+sb.data.cookie.userId],
                      system_name: toolToAdd.id,
                      display_name: toolToAdd.name,
                      is_archieved: 0,
                      order: toolOrder,
                      added_by: +sb.data.cookie.userId,
                      added_on: moment(),
                      settings: {},
                      box_color: toolToAdd.icon.color,
                    });

                    toolOrder++;
                  }
                );
                var hq = {
                  name: setupObj.instanceName,
                  parent: 0,
                  group_type: "Headquarters",
                  allowed_users: [+sb.data.cookie.userId],
                  managers: [+sb.data.cookie.userId],
                  tools: hqTools,
                };

                sb.data.db.obj.create("groups", hq, function (newHQ) {
                  var teams = [];
                  _.each(setupObj.teams, function (teamName) {
                    if (teamName != "") {
                      teamTools = [];
                      var toolOrder = 0;
                      _.each(
                        _.sortBy(appConfig.teamTools, "name"),
                        function (toolToAdd) {
                          teamTools.push({
                            allowed_users: [+sb.data.cookie.userId],
                            system_name: toolToAdd.id,
                            display_name: toolToAdd.name,
                            is_archieved: 0,
                            order: toolOrder,
                            added_by: +sb.data.cookie.userId,
                            added_on: moment(),
                            settings: {},
                            box_color: toolToAdd.icon.color,
                          });

                          toolOrder++;
                        }
                      );
                      var hq = {
                        name: setupObj.instanceName,
                        parent: 0,
                        group_type: "Headquarters",
                        allowed_users: [+sb.data.cookie.userId],
                        managers: [+sb.data.cookie.userId],
                        tools: hqTools,
                      };

                      teams.push({
                        name: teamName,
                        group_type: "Team",
                        allowed_users: [+sb.data.cookie.userId],
                        managers: [+sb.data.cookie.userId],
                        tagged_with: [0],
                        parent: hq.id,
                        tools: teamTools,
                      });
                    }
                  });

                  sb.data.db.obj.create(
                    "note_types",
                    {
                      name: "General",
                    },
                    function (r) {
                      if (teams.length > 0) {
                        sb.data.db.obj.create(
                          "groups",
                          teams,
                          function (newTeams) {
                            location.reload();
                          }
                        );
                      } else {
                        location.reload();
                      }
                    }
                  );
                });
              }.bind({}, dom, setupObj),
            },
          },
          sb.moduleId
        );

      dom.patch();
    }

    function setup(setupObj, callback) {
      function setupContactSystem(setupObj, callback) {
        function createClientTypes(infoTypes, callback) {
          sb.data.db.obj.getAll("company_categories", function (clientTypes) {
            if (clientTypes.length == 1000) {
              switch (setupObj.instanceSetupType) {
                case "non-profit":
                  var newClientTypes = [
                    {
                      available_types: _.pluck(infoTypes, "id"),
                      name: "Corporate Donor",
                    },
                    {
                      available_types: _.pluck(infoTypes, "id"),
                      name: "Family",
                    },
                  ];

                  break;

                default:
                  var newClientTypes = [
                    {
                      available_types: _.pluck(infoTypes, "id"),
                      name: "Client",
                    },
                    {
                      available_types: _.pluck(infoTypes, "id"),
                      name: "Vendor",
                    },
                  ];
              }

              sb.data.db.obj.create(
                "company_categories",
                newClientTypes,
                function (created) {
                  callback(created);
                }
              );
            } else {
              callback(clientTypes);
            }
          });
        }

        function createContactTypes(infoTypes, callback) {
          sb.data.db.obj.getAll("contact_types", function (contactTypes) {
            if (contactTypes.length == 1000) {
              switch (setupObj.instanceSetupType) {
                case "non-profit":
                  var newContactTypes = [
                    {
                      available_types: _.pluck(infoTypes, "id"),
                      name: "Donor",
                      states: [
                        {
                          color: "green",
                          icon: "check",
                          isEntryPoint: 1,
                          name: "Active",
                          next: ["2"],
                          previous: [],
                          uid: 1,
                        },
                        {
                          color: "grey",
                          icon: "ban",
                          name: "Inactive",
                          next: [],
                          previous: ["1"],
                          uid: 2,
                        },
                      ],
                    },
                    {
                      available_types: _.pluck(infoTypes, "id"),
                      name: "Volunteer",
                      states: [
                        {
                          color: "green",
                          icon: "check",
                          isEntryPoint: 1,
                          name: "Active",
                          next: ["2"],
                          previous: [],
                          uid: 1,
                        },
                        {
                          color: "grey",
                          icon: "ban",
                          name: "Inactive",
                          next: [],
                          previous: ["1"],
                          uid: 2,
                        },
                      ],
                    },
                  ];

                  break;

                default:
                  var newContactTypes = [
                    {
                      available_types: _.pluck(infoTypes, "id"),
                      name: "Lead",
                      states: [
                        {
                          color: "green",
                          icon: "check",
                          isEntryPoint: 1,
                          name: "Active",
                          next: ["2"],
                          previous: [],
                          uid: 1,
                        },
                        {
                          color: "grey",
                          icon: "ban",
                          name: "Inactive",
                          next: [],
                          previous: ["1"],
                          uid: 2,
                        },
                      ],
                    },
                    {
                      available_types: _.pluck(infoTypes, "id"),
                      name: "Client",
                      states: [
                        {
                          color: "green",
                          icon: "check",
                          isEntryPoint: 1,
                          name: "Active",
                          next: ["2"],
                          previous: [],
                          uid: 1,
                        },
                        {
                          color: "grey",
                          icon: "ban",
                          name: "Inactive",
                          next: [],
                          previous: ["1"],
                          uid: 2,
                        },
                      ],
                    },
                  ];
              }

              sb.data.db.obj.create(
                "contact_types",
                {
                  available_types: _.pluck(infoTypes, "id"),
                  name: "Contact",
                  states: newContactTypes,
                },
                function (created) {
                  callback(created);
                }
              );
            } else {
              callback(contactTypes);
            }
          });
        }

        function createInfoTypes(callback) {
          sb.data.db.obj.getAll("contact_info_types", function (infoTypes) {
            if (infoTypes.length == 0) {
              //create the default info types
              var newInfoTypes = [
                {
                  data_type: "email",
                  is_address: "",
                  name: "Email",
                  object_bp_type: '"contact_info_types"',
                },
                {
                  data_type: "phone",
                  is_address: "",
                  name: "Phone",
                  object_bp_type: '"contact_info_types"',
                },
                {
                  data_type: "address",
                  is_address: true,
                  name: "Default Address",
                  object_bp_type: '"contact_info_types"',
                },
                {
                  data_type: "website",
                  is_address: "",
                  name: "Website URL",
                  object_bp_type: '"contact_info_types"',
                },
              ];

              sb.data.db.obj.create(
                "contact_info_types",
                newInfoTypes,
                function (created) {
                  callback(created);
                }
              );
            } else {
              callback(infoTypes);
            }
          });
        }

        createInfoTypes(function (infoTypes) {
          createContactTypes(infoTypes, function (contactTypes) {
            createClientTypes(infoTypes, function (clientTypes) {
              callback(true);
            });
          });
        });
      }

      var workflowStates = [
        {
          uid: 1,
          name: "Planning",
          icon: "",
          previous: [],
          next: [2],
          isEntryPoint: 1,
        },
        {
          uid: 2,
          name: "In progress",
          icon: "",
          previous: [1],
          next: [3],
        },
        {
          uid: 3,
          name: "Done",
          icon: "",
          previous: [2],
          next: [],
        },
      ];

      switch (setupObj.instanceSetupType) {
        case "non-profit":
          setupObj.teams = [
            {
              name: "Board of Directors",
              description: "A team for the Board.",
            },
            {
              name: "Fundraising",
              description: "Keep track of fundraising campaigns.",
            },
            {
              name: "Volunteers",
              description: "Keep track of volunteer receruiting projects.",
            },
          ];

          break;

        case "sign-graphics":
          setupObj.teams = [
            {
              name: "Sales",
              description: "All the sales team things.",
            },
            {
              name: "Marketing",
              description: "All the marketing team things.",
            },
            {
              name: "Admin",
              description: "All the other things.",
            },
            {
              name: "Inventory",
              description: "All the other things.",
            },
          ];

          break;

        default:
          setupObj.teams = [
            {
              name: "Sales",
              description: "All the sales team things.",
            },
            {
              name: "Marketing",
              description: "All the marketing team things.",
            },
            {
              name: "Admin",
              description: "All the other things.",
            },
          ];
      }

      setupObj.projects = [
        {
          name: "Your first project",
          description: "This is a default project to get you started.",
        },
      ];

      hqTools = [];
      var toolOrder = 0;
      _.each(_.sortBy(appConfig.hqTools, "name"), function (toolToAdd) {
        switch (toolToAdd.id) {
          case "projectTool":
          case "crmTool":
          case "teamTool":
          case "taskTool":
          case "messageBoardTool":
          case "entityTool":
            hqTools.push({
              allowed_users: [+sb.data.cookie.userId],
              system_name: toolToAdd.id,
              display_name: toolToAdd.name,
              is_archieved: 0,
              order: toolOrder,
              added_by: +sb.data.cookie.userId,
              added_on: moment(),
              settings: {},
              box_color: toolToAdd.icon.color,
            });

            toolOrder++;

            break;
        }
      });

      var hq = {
        name: setupObj.instanceName,
        parent: 0,
        group_type: "Headquarters",
        allowed_users: [+sb.data.cookie.userId],
        managers: [+sb.data.cookie.userId],
        tools: hqTools,
      };

      sb.data.db.obj.create(
        "project_types",
        {
          states: workflowStates,
          name: "Regular Workflow",
        },
        function (workflow) {
          sb.data.db.obj.create("groups", hq, function (newHQ) {
            var teams = [];
            _.each(setupObj.teams, function (teamName) {
              if (teamName != "") {
                teamTools = [];
                var toolOrder = 0;
                _.each(
                  _.sortBy(appConfig.teamTools, "name"),
                  function (toolToAdd) {
                    switch (toolToAdd.id) {
                      case "projectTool":
                      case "crmTool":
                      case "teamTool":
                      case "taskTool":
                      case "messageBoardTool":
                      case "entityTool":
                        teamTools.push({
                          allowed_users: [+sb.data.cookie.userId],
                          system_name: toolToAdd.id,
                          display_name: toolToAdd.name,
                          is_archieved: 0,
                          order: toolOrder,
                          added_by: +sb.data.cookie.userId,
                          added_on: moment(),
                          settings: {},
                          box_color: toolToAdd.icon.color,
                        });

                        toolOrder++;

                        break;
                    }
                  }
                );

                teams.push({
                  name: teamName.name,
                  details: teamName.description,
                  group_type: "Team",
                  allowed_users: [+sb.data.cookie.userId],
                  managers: [+sb.data.cookie.userId],
                  tagged_with: [+sb.data.cookie.userId],
                  parent: hq.id,
                  tools: teamTools,
                });
              }
            });

            _.each(setupObj.projects, function (project) {
              if (project != "") {
                projectTools = [];
                var toolOrder = 0;
                _.each(
                  _.sortBy(appConfig.projectTools, "name"),
                  function (toolToAdd) {
                    projectTools.push({
                      allowed_users: [+sb.data.cookie.userId],
                      system_name: toolToAdd.id,
                      display_name: toolToAdd.name,
                      is_archieved: 0,
                      order: toolOrder,
                      added_by: +sb.data.cookie.userId,
                      added_on: moment(),
                      settings: {},
                      box_color: toolToAdd.icon.color,
                    });

                    toolOrder++;
                  }
                );

                teams.push({
                  name: project.name,
                  details: project.description,
                  type: workflow.id,
                  group_type: "Project",
                  allowed_users: [+sb.data.cookie.userId],
                  managers: [+sb.data.cookie.userId],
                  tagged_with: [+sb.data.cookie.userId],
                  parent: hq.id,
                  tools: projectTools,
                });
              }
            });

            sb.data.db.obj.create(
              "note_types",
              {
                name: "General",
                default: "yes",
              },
              function (r) {
                sb.data.db.obj.create("groups", teams, function (newTeams) {
                  // create the first users's mystuff group
                  var tools = [];

                  _.each(appConfig.myStuffTools, function (tool, toolOrder) {
                    switch (tool.id) {
                      case "myprojects":
                      case "messageBoardTool":
                      case "crmTool":
                      case "myteams":
                      case "taskTool":
                      case "entityList":
                        tools.push({
                          allowed_users: [+sb.data.cookie.userId],
                          system_name: tool.id,
                          display_name: tool.name,
                          is_archieved: 0,
                          order: toolOrder,
                          added_by: +sb.data.cookie.userId,
                          added_on: moment(),
                          settings: {},
                          box_color: tool.icon.color,
                        });

                        break;
                    }
                  });

                  sb.data.db.obj.create(
                    "groups",
                    {
                      group_type: "MyStuff",
                      user: +sb.data.cookie.userId,
                      name: appConfig.user.fname + " " + appConfig.user.lname,
                      tools: tools,
                    },
                    function (myStuffGroup) {
                      setupContactSystem(setupObj, function (done) {
                        callback(true);
                      });
                    }
                  );
                });
              }
            );
          });
        }
      );
    }

    function teams(dom, steps, setupObj) {
      var teamCount = 2;
      var teamForm = {
        teamName1: {
          label: "",
          name: "teamName1",
          type: "text",
          placeholder: "Sales Team",
          fieldCSS: "ui massive inverted fluid input",
        },
        teamName2: {
          label: "",
          name: "teamName2",
          type: "text",
          placeholder: "Accounting Team",
          fieldCSS: "ui massive inverted fluid input",
        },
      };

      dom.empty();

      dom
        .makeNode("grid", "div", {
          css: "ui stackable centered grid animated fadeIn",
        })
        .makeNode("col", "div", { css: "nine wide column" });

      dom.grid.col
        .makeNode("title", "div", { css: "ui huge header" })
        .makeNode("title", "div", { text: "Teams", css: "ui header" });

      dom.grid.col.makeNode("start", "div", {
        css: "ui header",
        text: "Organize people into easy to understand groups.",
      });

      dom.grid.col.makeNode("teamForm", "form", teamForm);

      dom.grid.col.makeNode("teamFormBreak", "div", { text: "<br />" });

      dom.grid.col
        .makeNode("addTeam", "div", {
          css: "ui huge button",
          text: "Add another team",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom) {
                teamCount++;

                teamForm["teamName" + teamCount] = {
                  name: "teamName" + teamCount,
                  label: "",
                  type: "text",
                  placeholder: "Development Team",
                  fieldCSS: "ui massive inverted fluid input",
                };

                dom.grid.col.makeNode("teamForm", "form", teamForm);

                dom.grid.col.patch();
              }.bind({}, dom),
            },
          },
          sb.moduleId
        );

      dom.grid.col
        .makeNode("skip", "div", { css: "ui blue huge button", text: "Skip" })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom, steps, setupObj) {
                setupObj.teams = [];

                steps("Workflows");

                workflows(dom, steps, setupObj);
              }.bind({}, dom, steps, setupObj),
            },
          },
          sb.moduleId
        );

      dom.grid.col
        .makeNode("go", "div", {
          css: "ui green huge button",
          text: "Next step",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom, steps, setupObj) {
                dom.grid.col.go.loading();

                var teams = [];
                var formInfo = dom.grid.col.teamForm.process().fields;

                if (teamCount > 0) {
                  var looping = 1;
                  while (looping <= teamCount) {
                    teams.push(formInfo["teamName" + looping].value);

                    looping++;
                  }
                }

                setupObj.teams = teams;

                steps("Workflows");

                workflows(dom, steps, setupObj);
              }.bind({}, dom, steps, setupObj),
            },
          },
          sb.moduleId
        );

      dom.patch();
    }

    function tips(dom, steps, setupObj) {
      dom.empty();

      dom
        .makeNode("grid", "div", {
          css: "ui stackable centered grid animated fadeIn",
        })
        .makeNode("col", "div", { css: "eight wide column" });

      dom.grid.col.makeNode("cards", "div", { css: "ui two stackable cards" });

      var cards = dom.grid.col.cards;

      cards
        .makeNode("hq", "div", { css: "ui card" })
        .makeNode("content", "div", { css: "content" });
      cards.hq.content.makeNode("header", "div", {
        css: "header",
        text: "Your Headquarters",
      });
      cards.hq.content.makeNode("meta", "div", {
        css: "meta",
        text: "Get a big picture view of the organization.",
      });
      cards.hq.content.makeNode("description", "div", {
        css: "description",
        text: "Your Headquarters is your organization as a whole. From the HQ you are able to see every Contact, Team, and Project that has been created.",
      });

      cards
        .makeNode("teams", "div", { css: "ui card" })
        .makeNode("content", "div", { css: "content" });
      cards.teams.content.makeNode("header", "div", {
        css: "header",
        text: "Teams",
      });
      cards.teams.content.makeNode("meta", "div", {
        css: "meta",
        text: "Group people, projects, and other resources.",
      });
      cards.teams.content.makeNode("description", "div", {
        css: "description",
        text: "These are the different teams of people that manage the various aspects of your organization.",
      });

      cards
        .makeNode("projects", "div", { css: "ui card" })
        .makeNode("content", "div", { css: "content" });
      cards.projects.content.makeNode("header", "div", {
        css: "header",
        text: "Projects",
      });
      cards.projects.content.makeNode("meta", "div", {
        css: "meta",
        text: "Jobs/Tickets/Events. ",
      });
      cards.projects.content.makeNode("description", "div", {
        css: "description",
        text: "Projects are how you keep track of your work. Workflows make it easy to track the status of those projects.",
      });

      cards
        .makeNode("tags", "div", { css: "ui card" })
        .makeNode("content", "div", { css: "content" });
      cards.tags.content.makeNode("header", "div", {
        css: "header",
        text: "Tags",
      });
      cards.tags.content.makeNode("meta", "div", {
        css: "meta",
        text: "What you call Permissions, we call Tags.",
      });
      cards.tags.content.makeNode("description", "div", {
        css: "description",
        text: "Tags are how we organize and share information in Bento. Want to share a contact with a team member? Add their tag. Want to share a project with a whole team. Add the Team Tag.",
      });

      dom
        .makeNode("btnGrid", "div", { css: "ui centered stackable grid" })
        .makeNode("btnCol", "div", { css: "five wide column center aligned" });

      dom.btnGrid.btnCol.makeNode("buttonBreak", "div", { text: "<br />" });
      dom.btnGrid.btnCol
        .makeNode("next", "div", {
          css: "ui huge green right labeled icon button",
          text: 'Continue <i class="right arrow icon"></i>',
        })
        .notify(
          "click",
          {
            type: "run-method",
            data: {
              run: function () {
                steps("HQ");
                headquarters(dom, steps, setupObj);
              },
            },
          },
          sb.moduleId
        );

      dom.patch();
    }

    function workflows(dom, steps, setupObj) {
      dom.empty();

      dom.makeNode("grid", "div", {
        css: "ui stackable centered grid animated fadeIn",
      });
      dom.grid.makeNode("col", "div", { css: "six wide column" });
      dom.grid
        .makeNode("col2", "div", { css: "four wide column" })
        .makeNode("cont", "div", { css: "ui blue segment" });

      dom.grid.col
        .makeNode("title", "div", { css: "ui huge header" })
        .makeNode("title", "div", {
          text: "Project Types & Workflows",
          css: "ui header",
        });

      dom.grid.col.makeNode("start1", "div", {
        css: "ui header",
        text: "What kind of projects do your teams work on?",
      });
      //dom.grid.col.makeNode('break1', 'div', {text:'<br />'});
      dom.grid.col.makeNode("title1", "div", {
        css: "",
        style: "font-size:1.2em;",
        text: "Things like a <b>sales team funnel</b> or maybe a <b>development team workflow</b>. These are the different types of projects your teammates will be able to create. Don't worry, you'll be able to change these later.",
      });
      dom.grid.col.makeNode("start2", "div", {
        css: "ui header",
        text: "What are your projects' workflows?",
      });
      dom.grid.col.makeNode("title2", "div", {
        css: "",
        style: "font-size:1.2em;",
        text: "Workflows let you set the different states a project can be in. A sales team project might start with <b>Discovery</b>, move to <b>Contracting</b>, and end with a <b>Signed</b> contract. That's a simple workflow, but you can create them as complicated as you need. Add more steps, change the names, move them around. Make them yours.",
      });
      dom.grid.col.makeNode("break2", "div", { text: "<br />" });
      dom.grid.col.makeNode("title2b", "div", {
        css: "",
        style: "font-size:1.2em;",
        text: "Each Project Type gets its own Workflow, so you can create completely seperate workflows for the different Teams in your Headquarters.",
      });
      //dom.grid.col.makeNode('break2', 'div', {text:'<br />'});
      //dom.grid.col.makeNode('start3', 'div', {css:'ui header', text:'You can change the name of the various phases to something that makes sense for your business. You can also add and subtract the number of phases in your workflow. You have the capability to customize your workflow to how you do business. If you would like to customize your workflow now, you can do so in the area to the right.'});

      dom.grid.col.makeNode("break3", "div", { text: "<br />" });

      dom.grid.col
        .makeNode("skip", "div", { css: "ui blue huge button", text: "Skip" })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom, steps) {
                steps("People");

                people(dom, steps, setupObj);
              }.bind({}, dom, steps, setupObj),
            },
          },
          sb.moduleId
        );

      dom.grid.col
        .makeNode("go", "div", {
          css: "ui green huge button",
          text: "Next step",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom, steps) {
                steps("People");

                people(dom, steps, setupObj);
              }.bind({}, dom, steps, setupObj),
            },
          },
          sb.moduleId
        );

      dom.patch();

      setupProjectTypes(function (projectType) {
        projectTypesView(dom.grid.col2.cont, projectType);
      });
    }

    function otherFunction() {
      dom.makeNode("title", "div", {
        text: "Welcome to Bento Systems.",
        css: "ui huge header",
      });

      dom.makeNode("start", "div", {
        css: "ui header",
        text: "Welcome to Bento Systems! Below are some details we need from you to get your customized system set up for you. Please review each section�s information and complete the following fields below.",
      });

      dom.makeNode("hqseg", "div", {
        css: "ui center aligned disabled segment",
      });
      dom.hqseg
        .makeNode("hq", "div", { css: "ui icon large header" })
        .makeNode("icon", "div", { tag: "i", css: "building icon" });
      dom.hqseg.hq.makeNode("content", "div", {
        css: "content",
        text: 'Headquarters<div class="sub header">Headquarters is the name of your organization. We have pre-populated the name of your headquarters based on the information you have given us. If you would like to change the name of your organization, please do so in the field below.</div>',
      });
      dom.hqseg.hq.makeNode("teamTitleBreak", "div", { text: "<br />" });
      dom.hqseg.hq.makeNode("hqForm", "form", {
        name: {
          label: "",
          name: "name",
          type: "text",
          placeholder: "Your company name here",
          value: appConfig.systemName,
          fieldCSS: "ui massive inverted fluid input",
        },
      });

      dom.makeNode("teamseg", "div", { css: "" });
      dom.teamseg
        .makeNode("teams", "div", { css: "ui icon large header" })
        .makeNode("icon", "div", { tag: "i", css: "users icon" });
      dom.teamseg.teams.makeNode("content", "div", {
        css: "content",
        text: 'Teams<div class="sub header">Teams are the different areas of focus for your organizations that various team members may be part of. For example, you may have a sales team, accounting team, administrative team, leadership team, etc. The name of your teams and the amount of teams you have is customizable to your business. To set up your Teams, fill out the fields below.</div>',
      });

      var teamCount = 2;
      var teamForm = {
        teamName1: {
          label: "",
          name: "teamName1",
          type: "text",
          placeholder: "Sales Team",
          fieldCSS: "ui massive inverted fluid input",
        },
        teamName2: {
          label: "",
          name: "teamName2",
          type: "text",
          placeholder: "Accounting Team",
          fieldCSS: "ui massive inverted fluid input",
        },
      };

      dom.teamseg.teams.makeNode("teamTitleBreak", "div", { text: "<br />" });

      dom.teamseg.teams.makeNode("teamForm", "form", teamForm);

      dom.teamseg.teams.makeNode("teamFormBreak", "div", { text: "<br />" });

      dom.teamseg.teams
        .makeNode("addTeam", "div", {
          css: "ui button",
          text: "Add another team",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom) {
                teamCount++;

                teamForm["name" + teamCount] = {
                  name: "name" + teamCount,
                  label: "",
                  type: "text",
                  placeholder: "Development Team",
                  fieldCSS: "ui massive inverted fluid input",
                };

                dom.teamseg.teams.makeNode("teamForm", "form", teamForm);

                dom.teamseg.teams.patch();
              }.bind({}, dom),
            },
          },
          sb.moduleId
        );

      dom
        .makeNode("workflowDivider", "div", {
          css: "ui horizontal divider section large header",
          text: "Workflows",
        })
        .makeNode("icon", "div", { tag: "i", css: "sitemap icon" });

      dom.makeNode("workflowSegs", "div", { css: "ui stackable grid" });
      dom.workflowSegs.makeNode("seg1", "div", { css: "eight wide column" });
      dom.workflowSegs.makeNode("seg2", "div", {
        css: "eight wide loading column",
      });

      dom.workflowSegs.seg1.makeNode("workflowHeader", "div", {
        text: "What is a Work Flow?",
        css: "ui header",
      });
      dom.workflowSegs.seg1.makeNode("workflowDetails1", "div", {
        text: "Nullam dictum finibus ornare. Pellentesque ullamcorper feugiat magna sit amet cursus. Mauris vel nisi leo. Praesent nec auctor quam. Proin nec dapibus quam. Phasellus quis est est. Phasellus nec felis ac enim tincidunt vestibulum nec vel sem. Praesent laoreet ipsum leo, id faucibus quam viverra at. Phasellus quis malesuada felis. Maecenas interdum mi tortor, a bibendum lacus euismod eu. Suspendisse ut sapien semper, aliquam tortor at, blandit nisi. Suspendisse potenti. Donec at lectus vitae magna rhoncus fringilla. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae;",
        css: "",
      });
      dom.workflowSegs.seg1.makeNode("break", "div", { text: "<br />" });
      dom.workflowSegs.seg1.makeNode("workflowDetails2", "div", {
        text: "Nullam dictum finibus ornare. Pellentesque ullamcorper feugiat magna sit amet cursus. Mauris vel nisi leo. Praesent nec auctor quam. Proin nec dapibus quam. Phasellus quis est est. Phasellus nec felis ac enim tincidunt vestibulum nec vel sem. Praesent laoreet ipsum leo, id faucibus quam viverra at. Phasellus quis malesuada felis. Maecenas interdum mi tortor, a bibendum lacus euismod eu. Suspendisse ut sapien semper, aliquam tortor at, blandit nisi. Suspendisse potenti. Donec at lectus vitae magna rhoncus fringilla. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae;",
        css: "",
      });
      dom.workflowSegs.seg1.makeNode("break2", "div", { text: "<br />" });
      dom.workflowSegs.seg1.makeNode("workflowDetails3", "div", {
        text: "Nullam dictum finibus ornare. Pellentesque ullamcorper feugiat magna sit amet cursus. Mauris vel nisi leo. Praesent nec auctor quam. Proin nec dapibus quam. Phasellus quis est est. Phasellus nec felis ac enim tincidunt vestibulum nec vel sem. Praesent laoreet ipsum leo, id faucibus quam viverra at. Phasellus quis malesuada felis. Maecenas interdum mi tortor, a bibendum lacus euismod eu. Suspendisse ut sapien semper, aliquam tortor at, blandit nisi. Suspendisse potenti. Donec at lectus vitae magna rhoncus fringilla. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae;",
        css: "",
      });
      dom.workflowSegs.seg2.makeNode("workflowDetails", "div", {
        text: "Create the first work flow.",
        css: "ui header",
      });
      var workflowCont = dom.workflowSegs.seg2.makeNode("cont", "div", {
        css: "ui blue segment",
      });
      dom.makeNode("workflowBreak", "div", { text: "<br />" });

      var peopleCount = 1;

      dom
        .makeNode("peopleDivider", "div", {
          css: "ui horizontal divider section large header",
          text: "People",
        })
        .makeNode("icon", "div", { tag: "i", css: "user circle icon" });

      //dom.makeNode('people', 'div', {text:'Invite people', css:'ui header'});
      dom.makeNode("peopleDetails", "div", {
        text: "Nullam dictum finibus ornare. Pellentesque ullamcorper feugiat magna sit amet cursus. Mauris vel nisi leo. Praesent nec auctor quam. Proin nec dapibus quam. Phasellus quis est est. Phasellus nec felis ac enim tincidunt vestibulum nec vel sem. Praesent laoreet ipsum leo, id faucibus quam viverra at. Phasellus quis malesuada felis. Maecenas interdum mi tortor, a bibendum lacus euismod eu. Suspendisse ut sapien semper, aliquam tortor at, blandit nisi. Suspendisse potenti. Donec at lectus vitae magna rhoncus fringilla. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia Curae;",
        css: "",
      });
      dom.makeNode("peopleCont", "div", {});
      dom.peopleCont.makeNode("peopleGrid" + peopleCount, "div", {
        css: "ui horizontal segments",
      });
      dom.peopleCont["peopleGrid" + peopleCount]
        .makeNode("col1", "div", { css: "ui blue segment" })
        .makeNode("fname", "form", {
          fname: {
            name: "fname",
            type: "text",
            label: "First Name",
            placeholder: "First Name",
          },
        });
      dom.peopleCont["peopleGrid" + peopleCount]
        .makeNode("col2", "div", { css: "ui blue segment" })
        .makeNode("lname", "form", {
          lname: {
            name: "lname",
            type: "text",
            label: "Last Name",
            placeholder: "Last Name",
          },
        });
      dom.peopleCont["peopleGrid" + peopleCount]
        .makeNode("col3", "div", { css: "ui blue segment" })
        .makeNode("email", "form", {
          email: {
            name: "email",
            type: "text",
            label: "Email Address",
            placeholder: "<EMAIL>",
          },
        });
      dom.peopleCont["peopleGrid" + peopleCount]
        .makeNode("col4", "div", { css: "ui blue segment" })
        .makeNode("phone", "form", {
          phone: {
            name: "phone",
            type: "text",
            label: "Cell Phone",
            placeholder: "************",
          },
        });

      //dom.makeNode('peopleFormBreak', 'div', {text:'<br />'});

      dom
        .makeNode("newPerson", "div", {
          text: "Add another person",
          tag: "button",
          css: "ui mini button",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom) {
                peopleCount++;

                dom.peopleCont.makeNode("peopleGrid" + peopleCount, "div", {
                  css: "ui horizontal segments",
                });

                dom.peopleCont["peopleGrid" + peopleCount]
                  .makeNode("col1", "div", { css: "ui blue segment" })
                  .makeNode("fname", "form", {
                    fname: {
                      name: "fname",
                      type: "text",
                      label: "First Name",
                      placeholder: "First Name",
                    },
                  });
                dom.peopleCont["peopleGrid" + peopleCount]
                  .makeNode("col2", "div", { css: "ui blue segment" })
                  .makeNode("lname", "form", {
                    lname: {
                      name: "lname",
                      type: "text",
                      label: "Last Name",
                      placeholder: "Last Name",
                    },
                  });
                dom.peopleCont["peopleGrid" + peopleCount]
                  .makeNode("col3", "div", { css: "ui blue segment" })
                  .makeNode("email", "form", {
                    email: {
                      name: "email",
                      type: "text",
                      label: "Email Address",
                      placeholder: "<EMAIL>",
                    },
                  });
                dom.peopleCont["peopleGrid" + peopleCount]
                  .makeNode("col4", "div", { css: "ui blue segment" })
                  .makeNode("phone", "form", {
                    phone: {
                      name: "phone",
                      type: "text",
                      label: "Cell Phone",
                      placeholder: "************",
                    },
                  });

                dom.peopleCont.patch();
              }.bind({}, dom),
            },
          },
          sb.moduleId
        );

      dom.makeNode("peopleBreak", "div", { text: "<br />" });

      dom.makeNode("continueDivider", "div", { css: "ui divider" });

      dom
        .makeNode("continue", "div", {
          css: "ui green huge button",
          text: "CONTINUE",
        })
        .notify(
          "click",
          {
            type: "headquarters-run",
            data: {
              run: function (dom, form, teamCount) {
                var formData = form.process();
                var instanceName = formData.fields.name.value;

                if (!instanceName) {
                  sb.dom.alerts.alert(
                    "",
                    "Please provide a name for your Headquarters.",
                    "error"
                  );
                  return;
                }

                var hq = {
                  name: instanceName,
                  parent: 0,
                  group_type: "Headquarters",
                  allowed_users: [+sb.data.cookie.userId],
                  managers: [+sb.data.cookie.userId],
                  tools: [
                    {
                      allowed_users: [+sb.data.cookie.userId],
                      system_name: "teamTool",
                      display_name: "Tools",
                      is_archieved: 0,
                      order: 0,
                      added_by: +sb.data.cookie.userId,
                      added_on: moment(),
                      settings: {},
                      box_color: "",
                    },
                    {
                      allowed_users: [+sb.data.cookie.userId],
                      system_name: "projectTool",
                      display_name: "Projects",
                      is_archieved: 0,
                      order: 0,
                      added_by: +sb.data.cookie.userId,
                      added_on: moment(),
                      settings: {},
                      box_color: "",
                    },
                  ],
                };

                dom.continue.loading();

                sb.data.db.obj.create("groups", hq, function (newHQ) {
                  var count = 0;
                  var teams = [];
                  while (count < teamCount) {
                    count++;

                    if (formData.fields["teamName" + count].value != "") {
                      teams.push({
                        name: formData.fields["teamName" + count].value,
                        group_type: "Team",
                        allowed_users: [+sb.data.cookie.userId],
                        managers: [+sb.data.cookie.userId],
                        tagged_with: [0],
                        parent: hq.id,
                        tools: [],
                      });
                    }
                  }

                  sb.data.db.obj.create("groups", teams, function (newTeams) {
                    location.reload();
                  });
                });
              }.bind({}, dom, dom.hqForm, teamCount),
            },
          },
          sb.moduleId
        );

      //dom.makeNode('finalBreak', 'div', {text:'<br />'});

      draw({
        dom: dom,
        after: function (dom) {
          dom.css("center");

          setupProjectTypes(function (projectType) {
            projectTypesView(workflowCont, projectType);
          });
        },
      });
    }

    mainDom.makeNode("logoBreak", "div", { text: "<br />" });
    mainDom.makeNode("logo", "div", { css: "ui centered header" });
    mainDom.logo.makeNode("image", "div", {
      tag: "img",
      src: "https://bento.infinityhospitality.net/img/Icons/bento.png",
      css: "ui image",
    });
    mainDom.logo.makeNode("content", "div", {
      css: "content",
      text: "Bento Systems",
    });

    mainDom.makeNode("logobreak", "div", { text: "<br />" });

    mainDom.makeNode("header", "div", {});
    mainDom.makeNode("break", "div", { text: "<br /><br />" });
    mainDom.makeNode("cont", "div", {});
    mainDom.makeNode("pageBreak", "div", { text: "<br /><br />" });

    draw({
      dom: mainDom,
      after: function (dom) {
        dom.css("");

        var steps = header.bind(dom.header);
        var setupObj = {};

        steps("Tips");

        tips(dom.cont, steps, setupObj);
      },
    });
  }

  return {
    initListeners: function () {
      sb.listen({
        "app-navigate-to": this.goToPage,
        "close-toolBar": this.closeToolBar,
        "open-toolBar": this.openToolBar,
        "refresh-toolBar": this.refreshToolBar,
        "refresh-app-page": this.refreshPage,
        "close-right-tray": this.closeRightTray,
        "open-right-tray": this.openRightTray,
        "register-application": this.register,
        "start-application": this.start,
        "run-method": this.run,
        "db-call-completed": this.updateCache,
        "update-notify-list": this.updateNotifyList,
        "get-instance-data": this.getInstanceData,
        "get-sys-modal": this.getSysModal,
        "get-sys-floater": this.getSysFloater,
        "get-mainUI": this.getMainUI,
        "move-elements-based-on-screen-size":
          this.moveElementsBasedOnScreenSize,

        // Listen to updates for cached data
        "field-updated": this.updateObjsCache,
      });

      sb.notify({
        type: "register-tool",
        data: {
          navigationItem: {
            moduleId: sb.moduleId,
            instanceId: sb.instanceId,
            id: "pages",
            title: "Pages",
            icon: '<i class="fa fa-lightbulb"></i>',
            views: [
              {
                id: "pagesTool",
                //type:'hqTool',
                layers: ["hq", "team", "myStuff", "project", "entity"],
                name: "Add Tools",
                tip: "Add new tools.",
                default: true,
                hide: true,
                icon: {
                  type: "plus",
                  color: "green",
                },
                mainViews: [
                  {
                    dom: function (dom, state, draw, mainDom) {
                      var pageURL = window.location.href;
                      var lastURLSegment = pageURL.substr(
                        pageURL.lastIndexOf("/") + 1
                      );

                      lastURLSegment = lastURLSegment.split("-");

                      lastURLSegment =
                        lastURLSegment[lastURLSegment.length - 1];

                      if (lastURLSegment === "pagesTool") {
                        onAddPagesArea = true;

                        DrawPageSelection();
                      }
                    },
                  },
                ],
                boxViews: [],
              },
            ],
          },
        },
      });
    },

    init: function () {},

    closeToolBar: function (data) {
      spaceNavigationColOpen = false;

      DrawToolBarMenu(data.state, 100, true);

      $("#spaceNavigationCol").removeClass("space-navigation-col-open");
      $("#spaceNavigationCol").addClass("space-navigation-col-closed");

      $("#openMenuButton").html('<i class="arrow right icon"></i>');

      menuWidth = "one";
    },

    openToolBar: function (data) {
      spaceNavigationColOpen = true;

      DrawToolBarMenu(data.state, 100, false);

      $("#spaceNavigationCol").removeClass("space-navigation-col-closed");
      $("#spaceNavigationCol").addClass("space-navigation-col-open");

      $("#openMenuButton").html('<i class="arrow left icon"></i>');

      menuWidth = "three";
    },

    refreshToolBar: function (data) {
      DrawToolBarMenu(data.state, 100, false, false);
    },

    closeRightTray: function (data) {
      rightTrayOpen = false;

      $("#rightTray").addClass("closed").removeClass("open");
      $("#rightTrayBoxView")
        .addClass("closed")
        .removeClass("three wide column open")
        .css({
          "padding-left": "0rem",
          "padding-right": "0rem",
          "box-shadow": "none",
        });
      $(".rightTrayBoxViewContainer").html("");
      $(".rightTrayBoxViewIcon").removeClass("active");

      // Show open/close right tray button on hover
      $("#rightTray.closed, #rightTrayBoxView.closed").hover(
        function () {
          $("#closeRightTrayButton").css("visibility", "hidden");
        },
        function () {
          $("#closeRightTrayButton").css("visibility", "hidden");
        }
      );

      $("#helpBtn, #helpBtn_Options").css({ right: "85px" });
      $(".versionUpdateContainer").css({ right: "105px" });

    },

    openRightTray: function (data) {
      rightTrayOpen = true;

      $("#rightTray").removeClass("closed").addClass("open");
      $("#rightTrayBoxView")
        .removeClass("closed")
        .addClass("three wide column open")
        .css({
          "padding-left": "1rem",
          "padding-right": "1rem",
          "box-shadow": "0 0 10px 0px rgba(0,0,0, 0.1)",
        });

      // Show open/close right tray button on hover
      $("#rightTray.open, #rightTrayBoxView.open").hover(
        function () {
          $("#closeRightTrayButton").css("visibility", "visible");
        },
        function () {
          $("#closeRightTrayButton").css("visibility", "hidden");
        }
      );

      $("#helpBtn, #helpBtn_Options").css({ right: "442px" });
      $(".versionUpdateContainer").css({ right: "470px" });

    },

    moveElementsBasedOnScreenSize: function (data) {
      if ($(data.window).width() <= 990) {
        moveElementsToMobile();
      } else {
        moveElementsToDesktop();
      }

      function moveElementsToDesktop() {
        $("#openMenuButton").show();
        $("#topNav").show();
        $("#mobileHeader").hide();
        $("#bottomGrid").prepend($("#spaceNavigationCol"));
        $("#bottomGrid").prepend($("#leftNav"));
        // $(bottomGrid.selector).append($(rightTray.selector));

        $("#rightTray").show();
      }

      function moveElementsToMobile() {
        $("#openMenuButton").hide();
        $("#topNav").hide();
        $("#mobileHeader").show();
        $("#mobileNav").append($("#leftNav"));
        $("#mobileNav").append($("#spaceNavigationCol"));
        // $(mobileNav_RightTray.selector).append($(rightTray.selector));

        $("#rightTray").hide();
      }
    },

    getMainUI: function (data) {
      data.callback(mainUI);
    },

    goToPage: function (data) {
      switch (data.type) {
        case "UP":
          var url = sb.data.url.createPageURL("UP");
          window.location.href = url;
          break;

        default:
          var obj = {};

          if (
            data.viewId &&
            data.viewId.viewState &&
            data.viewId.viewState.obj
          ) {
            obj = data.viewId.viewState.obj;
          } else if (data.viewId && data.viewId.rowObj) {
            obj = data.viewId.rowObj;
          }

          if (
            !obj.hasOwnProperty("name") &&
            obj.hasOwnProperty("fname") &&
            obj.hasOwnProperty("lname")
          ) {
            obj.name = obj.fname + " " + obj.lname;
          }

          if (data.object) {
            obj = data.object;
          }

          if (!_.isEmpty(obj)) {
            var type = "object";
            var setup = {
              id: obj.id,
              name: obj.name,
              type: obj.object_bp_type,
            };

            if (obj.hasOwnProperty("object_bp_type")) {
              if (obj.object_bp_type === "groups") {
                switch (obj.group_type) {
                  case "Project":
                    setup.type = "project";
                    break;

                  case "Team":
                    setup.type = "team";
                    break;

                  case "Task":
                    setup.type = "task";
                    break;
                }
              }
            }

            $(".ui.modal").modal("hide all");
            var url = sb.data.url.createPageURL(type, setup);
            window.location.href = url;
          }

          return;
      }
    },

    register: function (data) {
      if (data.navigationItem) {
        registerNavItems.push(data.navigationItem);
      }

      _.each(data.navigationItem.views, function (view) {
        if (view) {
          if (view.type === "tool") {
            if (!view.hiddenFromProjects) {
              projectTools.push(view);
            }

            objectTools.push(view);
          }

          if (view.type === "hqTool") {
            hqTools.push(view);
          }

          if (view.type === "teamTool") {
            teamTools.push(view);
          }

          if (view.type === "objectTool") {
            objectTools.push(view);
          }

          if (view.type === "myStuff") {
            myStuff.push(view);
          }

          if (view.type === "object-view") {
            objectViews.push(view);
          }

          if (view.type === "nav-item") {
            navItems.push(view);
          }

          if (view.type === "custom") {
            customViews.push(view);
          }

          if (view.type === "jobTypeTool") {
            jobTypeTools.push(view);
          }
        }
      });

      appConfig.navigation = _.sortBy(registerNavItems, "id");
    },

    refreshPage: function (data) {
      var pageParams = sb.data.url.getParams();
      var currentURL = location.pathname + location.hash;

      app_ui(App_UI, pageParams, undefined, true);
    },

    run: function (data) {
      data.run(data);
    },

    start: function (setup) {
      var redirectURL = sb.data.cookie.get("redirectURL");
      sb.data.cookie.set("redirectURL", "");

      RootInstance = {
        name: appConfig.instance,
        token: sb.data.cookie.get("token"),
      };
      appConfig.hqTools = hqTools;
      appConfig.teamTools = teamTools;
      appConfig.jobTypeTools = jobTypeTools;
      appConfig.projectTools = projectTools;
      appConfig.objectTools = objectTools;
      appConfig.myStuffTools = myStuff;
      appConfig.customTools = customViews;

      Components.editProfileView = sb.createComponent("userComponent");

      var pageParams = sb.data.url.getParams();
      var currentURL = location.pathname + location.hash;

      get_startup_data(pageParams, function (pageData) {
        // if(ga){
        // 	ga('set', 'userId', sb.data.cookie.get('uid'));
        // 	ga('send', 'pageview', currentURL);
        // }

        // if(_gs){
        // 	_gs('track');
        // }

        versionCheck();

        if (pageData.headquarters.hasOwnProperty("id")) {
          hq = pageData.headquarters;
          appConfig.headquarters = hq;

          if (setup.doneCallback) {
            app_ui(setup.domObj, pageParams, setup.doneCallback);
          } else {
            app_ui(setup.domObj, pageParams);
          }

          // Listen for page changes
          window.addEventListener(
            "hashchange",
            function (input) {
              if ($("#isWaitingToSave").val() == 1) {
                if (
                  confirm(
                    "You have unsaved changes, are you sure you want to leave?"
                  )
                ) {
                  $("#isWaitingToSave").val("");
                  processURLChange();
                } else {
                  window.history.replaceState("string", "", History[0]);
                }
              } else {
                processURLChange();
              }

              function processURLChange() {
                $(mainView_UI.selector).fadeOut(10, function () {
                  $(mainView_UI.selector).empty();
                  $(".ui.modal").modal("hide all");

                  sb.notify({
                    type: "page-changed",
                    data: {
                      page: pageParams,
                    },
                  });

                  var pageParams = sb.data.url.getParams();
                  var currentURL = location.pathname + location.hash;

                  app_ui(App_UI, pageParams, undefined, true);

                  // if(ga){
                  // 	ga('set', 'page', currentURL);
                  // 	ga('send', 'pageview');
                  // }

                  // if(_gs){
                  // 	_gs('track');
                  // }

                  History.unshift(window.location.href);
                  History.pop();
                });
              }
            },
            false
          );

          // If in a portal instance, route to the portal group.
          if (
            appConfig.is_portal
            // AND not in the portal group..
          ) {
            window.location.href = "#p-" + Portals[0].id;
          }

          if (redirectURL) {
            if (redirectURL != window.location.href) {
              window.history.replaceState(
                { additionalInformation: "" },
                appConfig.systemName,
                redirectURL
              );
              window.location.reload();
            }
          } else {
            // Remove loader gif
            setTimeout(function () {
              $("#main-loader").remove();
            }, 300);
          }
        } else {
          setup.domObj.makeNode("cont", "div", { css: "" });
          setup.domObj.build();

          // if(ga){
          // 	ga('send', {
          // 		hitType: 'event',
          // 		eventCategory: 'Accounts',
          // 		eventAction: 'Create'
          // 	});
          // }

          // if(_gs){

          // 	_gs('event', 'Account Created');

          // }

          welcomeUser(setup.domObj.cont, {}, function (dom) {
            dom.dom.patch();
            dom.after(dom.dom);

            setup.doneCallback();
          });
        }
      });
    },

    updateCache: function (data) {
      if (data.data != null) {
        Data.objects = _.filter(Data.objects, function (cachedObj) {
          if (cachedObj == undefined) {
            return false;
          }

          return cachedObj.id != data.data.id;
        });
      }
    },

    updateNotifyList: function (data) {
      if (_.matcher(data, { object: true, users: true })) {
        updateNotifyList(data.object, data.users);
      }
    },

    getInstanceData: function (data) {
      get_instance_data(data);
    },

    getSysModal: function (data) {
      if (typeof data.onClose === "function") {
        ModalOnClose = data.onClose;
      } else {
        ModalOnClose = function () {};
      }

      if (Modal.body) {
        Modal.body.empty();
        Modal.footer.empty();
        Modal.body.patch();
        Modal.footer.patch();
      } else if (!data.ui) {
        App_UI.mc.empty();
        Modal = App_UI.mc.makeNode("modal", "modal", {
          onClose: function () {
            ModalOnClose();
          },
        });
        App_UI.mc.patch();
      } else if (data.ui) {
        Modal = data.ui.makeNode("modal", "modal", {
          onClose: function () {
            ModalOnClose();
          },
        });
        data.ui.patch();
      }

      // Style
      if (data.style === "basic") {
        $(Modal.showSelector).addClass("basic");
      } else {
        $(Modal.showSelector).removeClass("basic");
      }

      // Size
      if (data.size) {
        $(Modal.showSelector).removeClass("large small tiny mini");
        $(Modal.showSelector).addClass(data.size);
      } else {
        $(Modal.showSelector).removeClass("large small tiny mini");
        $(Modal.showSelector).addClass("large");
      }

      // Scrolling
      if (data.scrolling == false) {
        $(Modal.showSelector + " .content").removeClass("scrolling");
      } else {
        $(Modal.showSelector + " .content").addClass("scrolling");
      }

      data.callback(Modal);
    },

    getSysFloater: function (data) {
      var targetElement = data.element;
      var width = data.width ? data.width : "auto";
      var minWidth = data.minWidth ? data.minWidth : "auto";
      var maxWidth = data.maxWidth ? data.maxWidth : "auto";
      var offsetTop = data.offsetTop ? data.offsetTop : 0;
      var padding = data.padding ? "padding: " + data.padding + ";" : "";

      function isOutOfViewport(element) {
        // Get the dom element
        element = document.querySelector(element);

        // Get element's bounding
        var bounding = element.getBoundingClientRect();

        // Check if it's out of the viewport on each side
        var out = {};
        out.top = bounding.top < 0;
        out.left = bounding.left < 0;
        out.bottom =
          bounding.bottom >
          (window.innerHeight || document.documentElement.clientHeight);
        out.right =
          bounding.right >
          (window.innerWidth || document.documentElement.clientWidth);
        out.any = out.top || out.left || out.bottom || out.right;
        out.all = out.top && out.left && out.bottom && out.right;

        return out;
      }

      function setFloaterPosition(element, targetElement, offsetTop) {
        if (!$(element.selector) || !$(targetElement).length) {
          return;
        }

        // Determine position
        var leftX = $(targetElement).offset().left + "px";
        var rightX = "auto";
        var topY =
          $(targetElement).offset().top -
          $(window).scrollTop() +
          offsetTop +
          "px";
        var arrowFloatAddClass = "arrow-float-left";
        var arrowFloatRemoveClass = "arrow-float-right";
        var isOut = isOutOfViewport(element.selector);
        if (isOut.right) {
          leftX = "auto";
          rightX =
            $(window).width() - $(targetElement).offset().left - 30 + "px";
          arrowFloatAddClass = "arrow-float-right";
          arrowFloatRemoveClass = "arrow-float-left";
          $(element.selector).attr("data-right-floated", rightX);
        }

        // Ensure that once the element is right floated, it stays that way
        if ($(element.selector).attr("data-right-floated")) {
          leftX = "auto";
          rightX = $(element.selector).attr("data-right-floated");
          arrowFloatAddClass = "arrow-float-right";
          arrowFloatRemoveClass = "arrow-float-left";
        }

        // Determine height
        var dropdownHeight =
          $(window).height() -
          $(targetElement).offset().top -
          $(targetElement)[0].offsetHeight -
          25 +
          "px";

        // Determine if inside a modal or not
        var zindex = 998;
        if ($(".modal.active").length > 0) {
          zindex = 10000;
        }

        $(element.selector).css({
          top: topY,
          left: leftX,
          right: rightX,
          "max-height": dropdownHeight,
          width: width,
          "min-width": minWidth,
          "max-width": maxWidth,
          "z-index": zindex,
        });
        $(element.selector)
          .addClass(arrowFloatAddClass)
          .removeClass(arrowFloatRemoveClass);

        if (!$(element.selector).attr("data-float-determined")) {
          $(element.selector).attr("data-float-determined", true);
          setFloaterPosition(element, targetElement, offsetTop);
        }
      }

      // Empty the current floater
      App_UI.floater.empty();

      // Create a new floater
      App_UI.floater.makeNode("floater", "div", {
        css: "ui floater",
      });

      App_UI.floater.floater.makeNode("container", "div", {
        css: "floater-container",
        style: padding,
      });

      App_UI.floater.floater.close = function (callback) {
        $(App_UI.floater.floater.selector).hide(0, function () {
          $(this).remove();

          if (typeof callback === "function") {
            callback();
          }
        });
      };

      // Patch the floater
      App_UI.floater.patch();

      // Set to global variable
      Floater = App_UI.floater.floater;

      // Set position when resizing the screen
      $(window).on("resize", function () {
        setFloaterPosition(Floater, targetElement, offsetTop);
      });

      // Set position when scrolling
      window.addEventListener(
        "scroll",
        function () {
          setFloaterPosition(Floater, targetElement, offsetTop);
        },
        true
      );

      // Set position on initialization
      setFloaterPosition(Floater, targetElement, offsetTop);

      // Hide when clicked out of
      $(document).mouseup(function (e) {
        var container = $(Floater.selector);
        if (!container.is(e.target) && container.has(e.target).length === 0) {
          container.hide();
        }
      });

      // Return the floater
      data.callback(Floater);
    },

    updateObjsCache: function (data) {
      if (data && data.obj && data.obj.id) {
        var toUpd = _.findWhere(Data.objects, {
          id: data.obj.id,
        });

        if (toUpd) {
          _.each(data.obj, function (val, key) {
            toUpd[key] = val;
          });
        }
      }
    },
  };
});
